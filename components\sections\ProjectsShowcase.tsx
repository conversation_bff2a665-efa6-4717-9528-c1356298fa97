"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ExternalLink, FolderGit2, Gith<PERSON> } from "lucide-react";
import { useState } from "react";

type Project = {
  title: string;
  description: string;
  technologies: string[];
  category: "frontend" | "fullstack" | "mobile";
  github: string;
  demo: string;
  image?: string;
};

export default function ProjectsShowcase() {
  const [filter, setFilter] = useState<string>("all");

  const projects: Project[] = [
    {
      title: "E-commerce Platform",
      description:
        "A full-featured e-commerce platform with cart, checkout, and payment integration.",
      technologies: ["Next.js", "TypeScript", "Stripe", "Tailwind"],
      category: "fullstack",
      github: "#",
      demo: "#",
    },
    // Add more projects...
  ];

  const categories = [
    { value: "all", label: "All Projects" },
    { value: "frontend", label: "Frontend" },
    { value: "fullstack", label: "Full Stack" },
    { value: "mobile", label: "Mobile" },
  ];

  const filteredProjects =
    filter === "all"
      ? projects
      : projects.filter((project) => project.category === filter);

  return (
    <section className="container mx-auto max-w-6xl px-4 py-24 sm:px-8 sm:py-32 lg:px-16">
      <div className="mb-16 space-y-4 text-center">
        <span className="mb-2 inline-block rounded-full bg-secondary px-3 py-1 text-sm font-medium">
          My Work
        </span>
        <h2 className="bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-3xl font-bold tracking-tight text-transparent sm:text-4xl">
          Featured Projects
        </h2>
        <p className="mx-auto max-w-[42rem] text-muted-foreground">
          A collection of projects that showcase my skills and experience
        </p>
      </div>

      {/* Filter Buttons */}
      <div className="mb-12 flex flex-wrap justify-center gap-4">
        {categories.map((category) => (
          <Button
            key={category.value}
            variant={filter === category.value ? "default" : "outline"}
            onClick={() => setFilter(category.value)}
            className="rounded-full"
          >
            {category.label}
          </Button>
        ))}
      </div>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
        {filteredProjects.map((project, index) => (
          <Card
            key={index}
            className="group transition-all duration-300 hover:shadow-xl"
          >
            <CardHeader>
              <CardTitle className="text-xl">{project.title}</CardTitle>
              <CardDescription>{project.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-6 flex flex-wrap gap-2">
                {project.technologies.map((tech) => (
                  <Badge key={tech} variant="secondary">
                    {tech}
                  </Badge>
                ))}
              </div>
              <div className="flex items-center justify-between">
                <div className="flex gap-3">
                  <a
                    href={project.github}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Button
                      variant="ghost"
                      size="icon"
                      className="hover:text-primary"
                    >
                      <Github className="h-5 w-5" />
                    </Button>
                  </a>
                  <a
                    href={project.demo}
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    <Button
                      variant="ghost"
                      size="icon"
                      className="hover:text-primary"
                    >
                      <ExternalLink className="h-5 w-5" />
                    </Button>
                  </a>
                </div>
                <FolderGit2 className="h-5 w-5 text-muted-foreground transition-colors group-hover:text-primary" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </section>
  );
}
