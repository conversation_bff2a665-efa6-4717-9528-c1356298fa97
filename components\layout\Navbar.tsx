"use client";

import { ThemeToggle } from "@/components/theme/theme-toggle";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { scrollToSection } from "@/utils/scroll-to-section";
import { motion, useScroll, useTransform } from "framer-motion";
import { useTheme } from "next-themes";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import MobileNav from "./MobileNav";

export default function Navbar() {
  const [scrolled, setScrolled] = useState(false);
  const [mounted, setMounted] = useState(false);
  const pathname = usePathname();
  const { theme } = useTheme();
  const { scrollY } = useScroll();

  // Ensure component is mounted to avoid hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  // Transform values for scroll animations
  const navbarBackground = useTransform(
    scrollY,
    [0, 50],
    ["rgba(255, 255, 255, 0)", "rgba(255, 255, 255, 0.95)"],
  );

  const navbarBackgroundDark = useTransform(
    scrollY,
    [0, 50],
    ["rgba(0, 0, 0, 0)", "rgba(0, 0, 0, 0.8)"],
  );

  const navbarHeight = useTransform(scrollY, [0, 100], ["5rem", "4rem"]);

  const navbarPadding = useTransform(scrollY, [0, 100], ["1rem", "0.5rem"]);

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleClick = (
    e: React.MouseEvent<HTMLAnchorElement>,
    path: string,
  ) => {
    if (path.startsWith("#") && window.location.pathname === "/") {
      e.preventDefault();
      scrollToSection(path.replace("#", ""));
    }
  };

  const navItems = [
    { href: "/", label: "Home", variant: "ghost" as const },
    { href: "/about", label: "About", variant: "ghost" as const },
    { href: "/skills", label: "Skills", variant: "ghost" as const },
    { href: "/projects", label: "Projects", variant: "ghost" as const },
    { href: "/experience", label: "Experience", variant: "ghost" as const },
    { href: "/services", label: "Services", variant: "ghost" as const },
    { href: "/tools", label: "Tools", variant: "ghost" as const },
    { href: "/contact", label: "Contact", variant: "default" as const },
  ];

  return (
    <motion.nav
      className={cn(
        "fixed top-0 z-50 w-full border-b transition-all duration-300",
        scrolled
          ? "border-border/50 shadow-sm backdrop-blur-md"
          : "border-transparent backdrop-blur-sm",
      )}
      style={{
        background: mounted
          ? theme === "dark"
            ? navbarBackgroundDark
            : navbarBackground
          : "transparent",
        height: navbarHeight,
        paddingTop: navbarPadding,
        paddingBottom: navbarPadding,
      }}
    >
      <div className="container flex h-full items-center justify-between">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Link
            href="/"
            className="font-heading text-2xl font-bold tracking-tight transition-colors hover:opacity-80"
          >
            <span
              className={cn(
                "inline-block bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500 bg-clip-text text-transparent",
                scrolled ? "drop-shadow-sm" : "",
              )}
            >
              Mohamed Zaki
            </span>
          </Link>
        </motion.div>

        {/* Desktop Navigation */}
        <div className="hidden items-center gap-1 lg:flex">
          {navItems.map((item, index) => {
            const isActive =
              (item.href === "/" && pathname === "/") ||
              (item.href !== "/" && pathname.startsWith(item.href));

            return (
              <motion.div
                key={item.href}
                initial={{ opacity: 0, y: -10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.3, delay: index * 0.05 }}
              >
                <Link
                  href={item.href}
                  onClick={(e) => handleClick(e, item.href)}
                >
                  <div className="relative px-1">
                    <Button
                      variant={isActive ? "default" : "ghost"}
                      className={cn(
                        "rounded-full font-medium transition-all",
                        isActive
                          ? "bg-primary text-primary-foreground"
                          : "hover:bg-primary/10 hover:text-primary dark:hover:bg-primary/5",
                        scrolled && !isActive ? "text-foreground/90" : "",
                      )}
                    >
                      {item.label}
                    </Button>

                    {isActive && (
                      <motion.div
                        className="bg-primary absolute -bottom-1 left-1/2 h-1 w-1 -translate-x-1/2 rounded-full"
                        layoutId="activeNavIndicator"
                        transition={{
                          type: "spring",
                          stiffness: 300,
                          damping: 30,
                        }}
                      />
                    )}
                  </div>
                </Link>
              </motion.div>
            );
          })}

          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.3, delay: navItems.length * 0.05 }}
            className="ml-2"
          >
            <ThemeToggle />
          </motion.div>
        </div>

        {/* Mobile Navigation */}
        <motion.div
          className="lg:hidden"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.3 }}
        >
          <MobileNav />
        </motion.div>
      </div>
    </motion.nav>
  );
}
