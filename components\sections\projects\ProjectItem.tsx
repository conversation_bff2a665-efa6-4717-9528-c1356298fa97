"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { type Project } from "@/types";
import { motion } from "framer-motion";
import { ExternalLink, Github } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface ProjectItemProps {
  project: Project;
}

export default function ProjectItem({ project }: ProjectItemProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <motion.div
        whileHover={{ y: -8 }}
        transition={{ type: "spring", stiffness: 300 }}
        className="group h-full cursor-pointer"
        onClick={() => setIsOpen(true)}
      >
        <div className="border-border from-background via-background to-background hover:border-primary/20 relative h-full overflow-hidden rounded-xl border bg-gradient-to-br transition-all duration-300 hover:shadow-lg">
          {/* Project thumbnail or placeholder */}
          <div className="relative h-48 w-full overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 via-blue-500/20 to-pink-500/20 opacity-70" />
            <div className="bg-background/30 absolute inset-0 flex items-center justify-center backdrop-blur-sm">
              <div className="text-center">
                <div className="mb-2 text-4xl">🚀</div>
                <div className="text-sm font-medium">{project.title}</div>
              </div>
            </div>
          </div>

          {/* Project content */}
          <div className="p-6">
            <div className="mb-4">
              <h3 className="font-heading mb-2 text-xl font-bold">
                {project.title}
              </h3>
              <p className="text-muted-foreground line-clamp-3 text-sm">
                {project.description}
              </p>
            </div>

            {/* Tech stack */}
            <div className="mb-6 flex flex-wrap gap-2">
              {project.tech.map((tech) => (
                <motion.span
                  key={tech}
                  whileHover={{ scale: 1.05 }}
                  className="bg-secondary/50 text-foreground/80 group-hover:bg-primary/10 group-hover:text-primary rounded-full px-3 py-1 text-xs font-medium transition-colors duration-300"
                >
                  {tech}
                </motion.span>
              ))}
            </div>

            {/* Action buttons */}
            <div className="flex gap-3">
              <Link
                href={project.github}
                target="_blank"
                className="flex-1"
                onClick={(e) => e.stopPropagation()}
              >
                <Button
                  variant="outline"
                  size="sm"
                  className="border-border hover:border-primary/50 hover:bg-primary/5 w-full gap-2 rounded-full transition-all duration-300"
                >
                  <Github className="h-4 w-4" />
                  Code
                </Button>
              </Link>
              <Link
                href={project.demo}
                target="_blank"
                className="flex-1"
                onClick={(e) => e.stopPropagation()}
              >
                <Button
                  variant="default"
                  size="sm"
                  className="w-full gap-2 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 transition-all duration-300 hover:from-purple-600 hover:to-blue-600"
                >
                  <ExternalLink className="h-4 w-4" />
                  Demo
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </motion.div>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="overflow-hidden p-0 sm:max-w-[700px]">
          {/* Header with gradient background */}
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 via-blue-500/20 to-pink-500/20" />
            <div className="relative p-6">
              <DialogHeader>
                <DialogTitle className="bg-gradient-to-r from-purple-500 to-blue-500 bg-clip-text text-3xl font-bold text-transparent">
                  {project.title}
                </DialogTitle>
                <DialogDescription className="text-muted-foreground mt-2 text-base">
                  {project.description}
                </DialogDescription>
              </DialogHeader>
            </div>
          </div>

          <div className="space-y-8 p-6">
            {/* Project Links Section */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 rounded-full bg-gradient-to-r from-purple-500 to-blue-500" />
                <h4 className="text-lg font-semibold">Project Links</h4>
              </div>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <Link href={project.github} target="_blank" className="flex-1">
                  <Button
                    variant="outline"
                    className="hover:bg-primary/5 hover:border-primary/50 h-12 w-full gap-3 text-base transition-all duration-300"
                  >
                    <Github className="h-5 w-5" />
                    View Source Code
                  </Button>
                </Link>
                <Link href={project.demo} target="_blank" className="flex-1">
                  <Button
                    variant="default"
                    className="h-12 w-full gap-3 bg-gradient-to-r from-purple-500 to-blue-500 text-base transition-all duration-300 hover:from-purple-600 hover:to-blue-600"
                  >
                    <ExternalLink className="h-5 w-5" />
                    Live Demo
                  </Button>
                </Link>
              </div>
            </div>

            {/* Features Section */}
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <div className="h-4 w-4 rounded-full bg-gradient-to-r from-purple-500 to-blue-500" />
                <h4 className="text-lg font-semibold">Key Features</h4>
              </div>
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                {project.tech.slice(0, 4).map((tech, index) => (
                  <div
                    key={index}
                    className="bg-secondary/30 flex items-center gap-3 rounded-lg p-4"
                  >
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-r from-purple-500/20 to-blue-500/20">
                      <span className="text-lg">✨</span>
                    </div>
                    <span className="text-sm font-medium">{tech}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
