"use client";

import GradientText from "@/components/effects/GradientText";
import { motion, useInView } from "framer-motion";
import { MessageSquare, Star, ThumbsUp } from "lucide-react";
import { useRef } from "react";

export default function Testimonials() {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  const testimonials = [
    {
      quote:
        "<PERSON> delivered a fantastic React application that exceeded our expectations. His attention to detail and technical expertise made the project a success.",
      author: "<PERSON>",
      position: "Product Manager",
      company: "TechVision",
      rating: 5,
      icon: <ThumbsUp className="h-5 w-5" />,
      color: "from-blue-500/20 to-cyan-500/20",
      textColor: "text-blue-500",
    },
    {
      quote:
        "Working with <PERSON> was a great experience. He understood our requirements perfectly and delivered a high-quality Next.js website on time.",
      author: "<PERSON>",
      position: "Startup Founder",
      company: "InnovateLabs",
      rating: 5,
      icon: <Star className="h-5 w-5" />,
      color: "from-amber-500/20 to-orange-500/20",
      textColor: "text-amber-500",
    },
    {
      quote:
        "The UI/UX improvements <PERSON> made to our application significantly enhanced user engagement and satisfaction. His expertise in modern frontend technologies is impressive.",
      author: "Alex Rodriguez",
      position: "Marketing Director",
      company: "GrowthTech",
      rating: 5,
      icon: <MessageSquare className="h-5 w-5" />,
      color: "from-purple-500/20 to-violet-500/20",
      textColor: "text-purple-500",
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 260,
        damping: 20,
      },
    },
  };

  return (
    <section
      className="relative overflow-hidden py-24 sm:py-32"
      ref={sectionRef}
    >
      {/* Background elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-amber-500/5" />
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:40px_40px] [mask-image:radial-gradient(white,transparent_85%)]" />
      </div>

      <div className="container">
        <motion.div
          className="mb-16 space-y-4 text-center"
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
        >
          <motion.span
            className="bg-primary/10 text-primary mb-2 inline-block rounded-full px-4 py-1.5 text-sm font-medium"
            variants={itemVariants}
          >
            Client Feedback
          </motion.span>
          <motion.h2
            className="font-heading text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl"
            variants={itemVariants}
          >
            <GradientText>What Clients Say</GradientText>
          </motion.h2>
          <motion.p
            className="text-muted-foreground mx-auto max-w-2xl text-lg"
            variants={itemVariants}
          >
            Hear from clients about their experience working with me
          </motion.p>
        </motion.div>

        <div className="mx-auto max-w-5xl">
          <div className="grid gap-8 md:grid-cols-3">
            {testimonials.map((testimonial, index) => (
              <motion.div
                key={index}
                className={`group relative overflow-hidden rounded-xl border border-transparent bg-gradient-to-br ${testimonial.color} p-1 backdrop-blur-sm transition-all duration-300 hover:shadow-xl`}
                initial={{ opacity: 0, y: 20 }}
                animate={
                  isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }
                }
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{
                  y: -5,
                  transition: { duration: 0.2 },
                }}
              >
                <div className="bg-background/80 h-full rounded-lg p-6">
                  {/* Quote mark */}
                  <div className={`mb-4 text-4xl ${testimonial.textColor}`}>
                    &ldquo;
                  </div>

                  {/* Testimonial content */}
                  <p className="text-muted-foreground mb-6">
                    {testimonial.quote}
                  </p>

                  {/* Rating stars */}
                  <div className="mb-4 flex">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 fill-current ${testimonial.textColor}`}
                      />
                    ))}
                  </div>

                  {/* Author info */}
                  <div className="flex items-center gap-3">
                    <div
                      className={`flex h-10 w-10 items-center justify-center rounded-full ${testimonial.color} ${testimonial.textColor}`}
                    >
                      {testimonial.icon}
                    </div>
                    <div>
                      <p className="font-semibold">{testimonial.author}</p>
                      <p className="text-muted-foreground text-sm">
                        {testimonial.position} at {testimonial.company}
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Decorative elements */}
          <div className="pointer-events-none absolute -bottom-16 -left-16 h-64 w-64 rounded-full bg-blue-500/5 blur-3xl" />
          <div className="pointer-events-none absolute -top-16 -right-16 h-64 w-64 rounded-full bg-purple-500/5 blur-3xl" />
        </div>
      </div>
    </section>
  );
}
