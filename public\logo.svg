<?xml version="1.0" encoding="UTF-8"?>
<svg width="640" height="640" viewBox="0 0 640 640" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Outer glow ring -->
  <circle cx="320" cy="320" r="310" fill="url(#outerGlow)" opacity="0.3"/>

  <!-- Main background with dark yellow gradient -->
  <circle cx="320" cy="320" r="288" fill="url(#bgGradient)"/>

  <!-- Animated rotating rings -->
  <circle cx="320" cy="320" r="260" stroke="url(#glowGradient)" stroke-width="4" stroke-dasharray="15 10" opacity="0.7">
    <animateTransform attributeName="transform" type="rotate" values="0 320 320;360 320 320" dur="8s" repeatCount="indefinite"/>
  </circle>
  <circle cx="320" cy="320" r="240" stroke="url(#pulseGradient)" stroke-width="2" stroke-dasharray="8 12" opacity="0.5">
    <animateTransform attributeName="transform" type="rotate" values="360 320 320;0 320 320" dur="12s" repeatCount="indefinite"/>
  </circle>

  <!-- Geometric tech pattern -->
  <g opacity="0.6">
    <!-- Hexagonal pattern -->
    <polygon points="320,200 360,220 360,260 320,280 280,260 280,220"
             stroke="url(#techGradient)" stroke-width="3" fill="none" opacity="0.8">
      <animate attributeName="stroke-opacity" values="0.3;1;0.3" dur="3s" repeatCount="indefinite"/>
    </polygon>
    <polygon points="320,360 360,380 360,420 320,440 280,420 280,380"
             stroke="url(#techGradient)" stroke-width="3" fill="none" opacity="0.8">
      <animate attributeName="stroke-opacity" values="1;0.3;1" dur="3s" repeatCount="indefinite"/>
    </polygon>

    <!-- Circuit lines -->
    <path d="M120 320h60M460 320h60M320 120v60M320 460v60"
          stroke="url(#circuitGradient)" stroke-width="3" stroke-dasharray="10 5" opacity="0.7">
      <animate attributeName="stroke-dashoffset" from="0" to="30" dur="4s" repeatCount="indefinite"/>
    </path>
    <path d="M200 200L280 280M440 200L360 280M200 440L280 360M440 440L360 360"
          stroke="url(#circuitGradient)" stroke-width="2" stroke-dasharray="8 6" opacity="0.6">
      <animate attributeName="stroke-dashoffset" from="0" to="28" dur="5s" repeatCount="indefinite"/>
    </path>
  </g>

  <!-- Enhanced M letter with 3D effect -->
  <g transform="translate(320,320)">
    <!-- Shadow layer -->
    <path d="M-120 128V-128L0 0L120 -128V128"
          stroke="#B45309"
          stroke-width="52"
          stroke-linecap="round"
          stroke-linejoin="round"
          opacity="0.4"
          transform="translate(4,4)"/>

    <!-- Main M with gradient stroke -->
    <path d="M-120 128V-128L0 0L120 -128V128"
          stroke="url(#mainStrokeGradient)"
          stroke-width="48"
          stroke-linecap="round"
          stroke-linejoin="round"/>

    <!-- Inner highlight -->
    <path d="M-120 128V-128L0 0L120 -128V128"
          stroke="url(#highlightGradient)"
          stroke-width="36"
          stroke-linecap="round"
          stroke-linejoin="round"/>

    <!-- Core glow -->
    <path d="M-120 128V-128L0 0L120 -128V128"
          stroke="url(#coreGradient)"
          stroke-width="24"
          stroke-linecap="round"
          stroke-linejoin="round"/>
  </g>

  <!-- Central energy core -->
  <circle cx="320" cy="320" r="40" fill="url(#energyCore)" opacity="0.9">
    <animate attributeName="r" values="35;45;35" dur="2s" repeatCount="indefinite"/>
  </circle>

  <!-- Pulsing energy ring -->
  <circle cx="320" cy="320" r="55" stroke="url(#energyRing)" stroke-width="4" stroke-dasharray="12 8" opacity="0.8">
    <animate attributeName="stroke-dashoffset" from="0" to="40" dur="2s" repeatCount="indefinite"/>
    <animate attributeName="r" values="50;60;50" dur="3s" repeatCount="indefinite"/>
  </circle>

  <!-- Corner energy nodes -->
  <g opacity="0.9">
    <circle cx="200" cy="192" r="20" fill="url(#nodeGradient)">
      <animate attributeName="r" values="18;24;18" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="440" cy="192" r="20" fill="url(#nodeGradient)">
      <animate attributeName="r" values="24;18;24" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="200" cy="448" r="20" fill="url(#nodeGradient)">
      <animate attributeName="r" values="18;24;18" dur="2.5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="440" cy="448" r="20" fill="url(#nodeGradient)">
      <animate attributeName="r" values="24;18;24" dur="2.5s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Energy beams -->
  <g opacity="0.7">
    <path d="M320 280L320 360" stroke="url(#beamGradient)" stroke-width="8" stroke-linecap="round">
      <animate attributeName="stroke-width" values="6;12;6" dur="2s" repeatCount="indefinite"/>
    </path>
    <path d="M280 320L360 320" stroke="url(#beamGradient)" stroke-width="8" stroke-linecap="round">
      <animate attributeName="stroke-width" values="12;6;12" dur="2s" repeatCount="indefinite"/>
    </path>
  </g>

  <!-- Particle effects -->
  <g opacity="0.6">
    <circle cx="250" cy="250" r="3" fill="#FCD34D">
      <animate attributeName="cy" values="250;150;250" dur="4s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="390" cy="390" r="3" fill="#FCD34D">
      <animate attributeName="cy" values="390;490;390" dur="5s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="370" cy="270" r="2" fill="#F59E0B">
      <animate attributeName="cx" values="370;470;370" dur="6s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="6s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Enhanced gradients with dark yellow theme -->
  <defs>
    <radialGradient id="outerGlow" cx="0.5" cy="0.5" r="0.5">
      <stop offset="0%" stop-color="#F59E0B" stop-opacity="0.1"/>
      <stop offset="100%" stop-color="#D97706" stop-opacity="0.3"/>
    </radialGradient>

    <radialGradient id="bgGradient" cx="0.3" cy="0.3" r="0.8">
      <stop offset="0%" stop-color="#FCD34D"/>
      <stop offset="30%" stop-color="#F59E0B"/>
      <stop offset="70%" stop-color="#D97706"/>
      <stop offset="100%" stop-color="#B45309"/>
    </radialGradient>

    <linearGradient id="glowGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#FCD34D" stop-opacity="0.9"/>
      <stop offset="50%" stop-color="#F59E0B" stop-opacity="0.7"/>
      <stop offset="100%" stop-color="#D97706" stop-opacity="0.9"/>
    </linearGradient>

    <linearGradient id="pulseGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#FCD34D" stop-opacity="0.6"/>
      <stop offset="50%" stop-color="#F59E0B" stop-opacity="0.3"/>
      <stop offset="100%" stop-color="#D97706" stop-opacity="0.6"/>
    </linearGradient>

    <linearGradient id="techGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#FCD34D"/>
      <stop offset="100%" stop-color="#F59E0B"/>
    </linearGradient>

    <linearGradient id="circuitGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#F59E0B"/>
      <stop offset="50%" stop-color="#FCD34D"/>
      <stop offset="100%" stop-color="#D97706"/>
    </linearGradient>

    <linearGradient id="mainStrokeGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#B45309"/>
      <stop offset="50%" stop-color="#D97706"/>
      <stop offset="100%" stop-color="#92400E"/>
    </linearGradient>

    <linearGradient id="highlightGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#F59E0B"/>
      <stop offset="50%" stop-color="#FCD34D"/>
      <stop offset="100%" stop-color="#F59E0B"/>
    </linearGradient>

    <linearGradient id="coreGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#FEF3C7"/>
      <stop offset="50%" stop-color="#FCD34D"/>
      <stop offset="100%" stop-color="#F59E0B"/>
    </linearGradient>

    <radialGradient id="energyCore" cx="0.5" cy="0.5" r="0.5">
      <stop offset="0%" stop-color="#FEF3C7"/>
      <stop offset="30%" stop-color="#FCD34D"/>
      <stop offset="70%" stop-color="#F59E0B"/>
      <stop offset="100%" stop-color="#D97706"/>
    </radialGradient>

    <linearGradient id="energyRing" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#FCD34D"/>
      <stop offset="50%" stop-color="#F59E0B"/>
      <stop offset="100%" stop-color="#FCD34D"/>
    </linearGradient>

    <radialGradient id="nodeGradient" cx="0.5" cy="0.5" r="0.5">
      <stop offset="0%" stop-color="#FEF3C7"/>
      <stop offset="50%" stop-color="#FCD34D"/>
      <stop offset="100%" stop-color="#F59E0B"/>
    </radialGradient>

    <linearGradient id="beamGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#FCD34D" stop-opacity="0.8"/>
      <stop offset="50%" stop-color="#F59E0B"/>
      <stop offset="100%" stop-color="#FCD34D" stop-opacity="0.8"/>
    </linearGradient>
  </defs>
</svg>