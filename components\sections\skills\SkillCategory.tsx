"use client";

import { Skill } from "@/types";
import { motion } from "framer-motion";
import { useRef } from "react";
import SkillItem from "./SkillItem";

interface SkillCategoryProps {
  category: string;
  skills: Skill[];
  index: number;
}

export default function SkillCategory({
  category,
  skills,
  index,
}: SkillCategoryProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  // Different background colors based on category
  const getCategoryColor = (category: string) => {
    switch (category) {
      case "Frontend":
        return "from-blue-600/20 to-cyan-600/20";
      case "Backend":
        return "from-green-600/20 to-emerald-600/20";
      case "Tools":
        return "from-purple-600/20 to-violet-600/20";
      case "Soft Skills":
        return "from-amber-600/20 to-orange-600/20";
      default:
        return "from-gray-600/20 to-slate-600/20";
    }
  };

  // Different text colors based on category
  const getCategoryTextColor = (category: string) => {
    switch (category) {
      case "Frontend":
        return "text-blue-500";
      case "Backend":
        return "text-green-500";
      case "Tools":
        return "text-purple-500";
      case "Soft Skills":
        return "text-amber-500";
      default:
        return "text-gray-500";
    }
  };

  // Get icon for category
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "Frontend":
        return "👨‍💻";
      case "Backend":
        return "🔧";
      case "Tools":
        return "🛠️";
      case "Soft Skills":
        return "🧠";
      default:
        return "🔍";
    }
  };

  const scrollLeft = () => {
    if (containerRef.current) {
      containerRef.current.scrollBy({ left: -300, behavior: "smooth" });
    }
  };

  const scrollRight = () => {
    if (containerRef.current) {
      containerRef.current.scrollBy({ left: 300, behavior: "smooth" });
    }
  };

  return (
    <motion.div
      className="mb-16"
      initial={{ opacity: 0, y: 30 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: index * 0.2 }}
    >
      {/* Category header */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div
            className={`flex h-12 w-12 items-center justify-center rounded-xl bg-gradient-to-br ${getCategoryColor(
              category,
            )} text-2xl shadow-lg`}
          >
            {getCategoryIcon(category)}
          </div>
          <h3
            className={`font-heading text-2xl font-bold ${getCategoryTextColor(
              category,
            )}`}
          >
            {category}
          </h3>
        </div>

        {/* Navigation arrows */}
        <div className="flex gap-2">
          <button
            onClick={scrollLeft}
            className="border-border bg-background/80 hover:bg-background flex h-10 w-10 items-center justify-center rounded-full border backdrop-blur-sm transition-all hover:scale-105"
            aria-label="Scroll left"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="m15 18-6-6 6-6" />
            </svg>
          </button>
          <button
            onClick={scrollRight}
            className="border-border bg-background/80 hover:bg-background flex h-10 w-10 items-center justify-center rounded-full border backdrop-blur-sm transition-all hover:scale-105"
            aria-label="Scroll right"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="m9 18 6-6-6-6" />
            </svg>
          </button>
        </div>
      </div>

      {/* Horizontal scrollable container */}
      <div
        ref={containerRef}
        className="no-scrollbar flex snap-x snap-mandatory gap-6 overflow-x-auto pb-4"
      >
        {skills.map((skill, idx) => (
          <div key={skill.name} className="min-w-[280px] snap-start">
            <SkillItem skill={skill} index={idx} />
          </div>
        ))}
      </div>
    </motion.div>
  );
}
