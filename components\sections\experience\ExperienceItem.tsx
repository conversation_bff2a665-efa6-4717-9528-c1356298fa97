"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from "@/components/ui/card";
import { Experience } from "@/types";
import { motion } from "framer-motion";
import { MapPin } from "lucide-react";

function calculateDuration(startDate: Date, endDate: Date): string {
  const years = endDate.getFullYear() - startDate.getFullYear();
  const months = endDate.getMonth() - startDate.getMonth();
  const totalMonths = years * 12 + months;
  const displayYears = Math.floor(totalMonths / 12);
  const displayMonths = totalMonths % 12;
  return `${displayYears > 0 ? `${displayYears} year${displayYears > 1 ? "s" : ""} ` : ""}${displayMonths > 0 ? `${displayMonths} month${displayMonths > 1 ? "s" : ""}` : ""}`.trim();
}

type ExperienceItemProps = {
  exp: Experience;
  index: number;
  animate?: "visible" | "hidden";
  initial?: "visible" | "hidden";
};

export default function ExperienceItem({ exp, index }: ExperienceItemProps) {
  const isEven = index % 2 === 0;

  const itemVariants = {
    hidden: {
      opacity: 0,
      x: isEven ? 50 : -50,
    },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const dotVariants = {
    hidden: { scale: 0 },
    visible: {
      scale: 1,
      transition: {
        duration: 0.4,
        delay: 0.2,
      },
    },
  };

  return (
    <motion.div
      className={`relative flex items-center ${isEven ? "md:flex-row-reverse" : "md:flex-row"}`}
      variants={itemVariants}
    >
      {/* Timeline dot */}
      <motion.div
        className="border-background bg-primary outline-border absolute left-0 ml-[1px] h-4 w-4 !-translate-x-1/2 transform rounded-full border-4 outline-[2px] md:left-1/2"
        variants={dotVariants}
      />

      {/* Content */}
      <div className="w-full md:w-1/2 md:px-8">
        <motion.div
          whileHover={{ y: -8 }}
          transition={{ type: "spring", stiffness: 300 }}
        >
          <Card className="m-5 transition-all duration-300 hover:shadow-lg">
            <CardHeader className="p-6">
              <div className="space-y-2">
                <CardTitle className="text-xl">{exp.role}</CardTitle>
                <p className="text-primary font-medium">{exp.company}</p>

                {exp.location && (
                  <div className="text-muted-foreground flex items-center text-sm">
                    <MapPin className="mr-1 h-4 w-4" />
                    <span>{exp.location}</span>
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <span className="bg-secondary text-muted-foreground inline-block rounded-full px-4 py-1 text-sm">
                    {exp.period}
                  </span>
                  <span className="text-muted-foreground block text-sm">
                    Duration: {calculateDuration(exp.startDate, new Date())}
                  </span>
                </div>
              </div>
            </CardHeader>
            <CardContent className="px-6 pb-6">
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.3 }}
                className="space-y-6"
              >
                <div>
                  <h4 className="mb-3 text-sm font-medium">
                    Responsibilities:
                  </h4>
                  <ul className="text-muted-foreground list-disc space-y-2 pl-5">
                    {exp.description
                      .split(". ")
                      .filter((item) => item.trim().length > 0)
                      .map((item, i) => (
                        <motion.li
                          key={i}
                          initial={{ opacity: 0, x: 10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{
                            duration: 0.3,
                            delay: 0.6 + i * 0.1,
                            ease: "easeOut",
                          }}
                          className="leading-relaxed"
                        >
                          {item.trim().endsWith(".")
                            ? item.trim()
                            : `${item.trim()}.`}
                        </motion.li>
                      ))}
                  </ul>
                </div>

                {exp.achievements && exp.achievements.length > 0 && (
                  <div>
                    <h4 className="mb-3 text-sm font-medium">
                      Key Achievements:
                    </h4>
                    <ul className="text-muted-foreground list-disc space-y-2 pl-5">
                      {exp.achievements.map((achievement, i) => (
                        <motion.li
                          key={i}
                          initial={{ opacity: 0, x: 10 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{
                            duration: 0.3,
                            delay: 0.8 + i * 0.1,
                            ease: "easeOut",
                          }}
                          className="leading-relaxed"
                        >
                          {achievement}
                        </motion.li>
                      ))}
                    </ul>
                  </div>
                )}

                {exp.technologies && exp.technologies.length > 0 && (
                  <div>
                    <h4 className="mb-3 text-sm font-medium">Technologies:</h4>
                    <div className="flex flex-wrap gap-2">
                      {exp.technologies.map((tech, i) => (
                        <motion.div
                          key={i}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{
                            duration: 0.3,
                            delay: 1 + i * 0.05,
                            ease: "easeOut",
                          }}
                        >
                          <Badge variant="outline" className="bg-secondary/50">
                            {tech}
                          </Badge>
                        </motion.div>
                      ))}
                    </div>
                  </div>
                )}
              </motion.div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Spacer for alternate layout */}
      <div className="hidden w-1/2 md:block" />
    </motion.div>
  );
}
