"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, CardFooter } from "@/components/ui/card";
import { certifications } from "@/data";
import { Education as EducationType } from "@/types";
import { motion, useInView } from "framer-motion";
import { BookOpen, ExternalLink, GraduationCap, Medal } from "lucide-react";
import { useRef } from "react";
import { FaSchool } from "react-icons/fa";
import { SiCoursera, SiFreecodecamp, SiUdemy } from "react-icons/si";

export default function Education() {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  const cardVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut",
      },
    },
  };

  const education: EducationType[] = [
    {
      degree: "Bachelor in Engineering",
      school:
        "Faculty of Engineering, Mansoura University - Computer Control Systems Engineering Department (CSED)",
      period: "2019 - 2023",
      grade: "Excellent",
      description:
        "Specialized in computer systems engineering with a focus on software development and control systems. Completed coursework in algorithms, data structures, software engineering, and web development.",
    },
  ];

  const getCertificationIcon = (issuer: string) => {
    switch (issuer.toLowerCase()) {
      case "meta (facebook)":
        return <Medal className="h-6 w-6" />;
      case "freecodecamp":
        return <SiFreecodecamp className="h-6 w-6" />;
      case "udemy":
        return <SiUdemy className="h-6 w-6" />;
      case "coursera":
        return <SiCoursera className="h-6 w-6" />;
      default:
        return <BookOpen className="h-6 w-6" />;
    }
  };

  return (
    <section id="education" className="bg-secondary/50 py-24" ref={sectionRef}>
      <div className="container mx-auto">
        <motion.div
          className="mb-16 space-y-4 text-center"
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
        >
          <motion.div
            className="mb-2 flex justify-center"
            variants={itemVariants}
          >
            <div className="bg-primary/10 text-primary rounded-full p-3">
              <GraduationCap className="h-6 w-6" />
            </div>
          </motion.div>
          <motion.span
            className="bg-primary/10 text-primary mb-2 inline-block rounded-full px-4 py-1.5 text-sm font-medium"
            variants={itemVariants}
          >
            Education & Certifications
          </motion.span>
          <motion.h2
            className="font-heading text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl"
            variants={itemVariants}
          >
            <span className="from-primary to-primary/60 bg-gradient-to-r bg-clip-text text-transparent">
              Learning Journey
            </span>
          </motion.h2>
          <motion.p
            className="text-muted-foreground mx-auto max-w-[42rem] text-lg"
            variants={itemVariants}
          >
            My academic foundation and continuous professional development
          </motion.p>
        </motion.div>

        {/* Formal Education */}
        <motion.div
          className="mb-16"
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
        >
          <motion.h3
            className="mb-8 text-center text-2xl font-bold"
            variants={itemVariants}
          >
            Formal Education
          </motion.h3>
          <motion.div
            className="mx-auto max-w-4xl"
            variants={containerVariants}
          >
            {education.map((edu) => (
              <motion.div key={edu.degree} variants={cardVariants}>
                <motion.div
                  whileHover={{ y: -8 }}
                  transition={{ type: "spring", stiffness: 300 }}
                >
                  <Card className="border-primary/20 transition-all duration-300 hover:shadow-xl">
                    <CardContent className="p-8">
                      <div className="flex items-start gap-4">
                        <div className="bg-primary/10 text-primary rounded-lg p-3">
                          <FaSchool className="h-6 w-6" />
                        </div>
                        <div className="flex-1 space-y-5">
                          <h3 className="text-xl font-semibold">
                            {edu.degree}
                          </h3>
                          <p className="text-muted-foreground">{edu.school}</p>
                          {edu.description && (
                            <p className="text-muted-foreground text-sm">
                              {edu.description}
                            </p>
                          )}
                          <div className="flex items-center justify-between">
                            <span className="bg-secondary text-muted-foreground rounded-full px-3 py-1 text-xs">
                              {edu.period}
                            </span>
                            <span className="text-primary text-sm font-medium">
                              Grade: {edu.grade}
                            </span>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>

        {/* Professional Certifications */}
        <motion.div
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
        >
          <motion.h3
            className="mb-8 text-center text-2xl font-bold"
            variants={itemVariants}
          >
            Professional Certifications
          </motion.h3>
          <div className="mx-auto grid max-w-5xl grid-cols-1 gap-6 md:grid-cols-2">
            {certifications.map((cert, index) => (
              <motion.div
                key={cert.title}
                variants={cardVariants}
                custom={index}
                whileHover={{ y: -5, scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Card className="border-primary/10 flex h-full flex-col transition-all duration-300 hover:shadow-lg">
                  <CardContent className="flex-grow p-6">
                    <div className="flex items-start gap-4">
                      <div className="bg-primary/10 text-primary shrink-0 rounded-lg p-3">
                        {cert.icon || getCertificationIcon(cert.issuer)}
                      </div>
                      <div className="space-y-3">
                        <h4 className="text-lg font-medium">{cert.title}</h4>
                        <div className="text-muted-foreground flex items-center text-sm">
                          <span className="font-medium">{cert.issuer}</span>
                          <span className="mx-2">•</span>
                          <span>{cert.date}</span>
                        </div>
                        {cert.credentialId && (
                          <p className="text-muted-foreground text-xs">
                            Credential ID: {cert.credentialId}
                          </p>
                        )}
                      </div>
                    </div>
                  </CardContent>
                  {cert.credentialURL && (
                    <CardFooter className="p-4 pt-0">
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full"
                        asChild
                      >
                        <a
                          href={cert.credentialURL}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center justify-center gap-2"
                        >
                          <span>View Certificate</span>
                          <ExternalLink className="h-4 w-4" />
                        </a>
                      </Button>
                    </CardFooter>
                  )}
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
}
