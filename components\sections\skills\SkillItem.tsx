import { Skill } from "@/types";
import { motion, useMotionValue, useSpring, useTransform } from "framer-motion";
import { useState } from "react";

interface SkillItemProps {
  skill: Skill;
  index: number;
}

export default function SkillItem({ skill, index }: SkillItemProps) {
  const [isFlipped, setIsFlipped] = useState(false);
  const x = useMotionValue(0);
  const y = useMotionValue(0);

  // Create smooth spring-based values for the tilt effect
  const rotateX = useSpring(useTransform(y, [-100, 100], [10, -10]), {
    stiffness: 300,
    damping: 30,
  });
  const rotateY = useSpring(useTransform(x, [-100, 100], [-10, 10]), {
    stiffness: 300,
    damping: 30,
  });

  function handleMouseMove(e: React.MouseEvent<HTMLDivElement>) {
    const rect = e.currentTarget.getBoundingClientRect();
    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;
    x.set(e.clientX - centerX);
    y.set(e.clientY - centerY);
  }

  function handleMouseLeave() {
    x.set(0);
    y.set(0);
  }

  // Different background colors based on category
  const getCategoryGradient = (category: string) => {
    switch (category) {
      case "Frontend":
        return "from-blue-600/20 via-blue-500/10 to-cyan-400/20";
      case "Backend":
        return "from-green-600/20 via-green-500/10 to-emerald-400/20";
      case "Tools":
        return "from-purple-600/20 via-purple-500/10 to-violet-400/20";
      case "Soft Skills":
        return "from-amber-600/20 via-amber-500/10 to-orange-400/20";
      default:
        return "from-gray-600/20 via-gray-500/10 to-slate-400/20";
    }
  };

  // Different text colors based on category
  const getCategoryTextColor = (category: string) => {
    switch (category) {
      case "Frontend":
        return "text-blue-500";
      case "Backend":
        return "text-green-500";
      case "Tools":
        return "text-purple-500";
      case "Soft Skills":
        return "text-amber-500";
      default:
        return "text-gray-500";
    }
  };

  // Get progress bar color based on category
  const getProgressBarColor = (category: string) => {
    switch (category) {
      case "Frontend":
        return "bg-blue-500";
      case "Backend":
        return "bg-green-500";
      case "Tools":
        return "bg-purple-500";
      case "Soft Skills":
        return "bg-amber-500";
      default:
        return "bg-gray-500";
    }
  };

  // Different border colors based on category
  const getCategoryBorderColor = (category: string) => {
    switch (category) {
      case "Frontend":
        return "border-blue-500/20";
      case "Backend":
        return "border-green-500/20";
      case "Tools":
        return "border-purple-500/20";
      case "Soft Skills":
        return "border-amber-500/20";
      default:
        return "border-gray-500/20";
    }
  };

  return (
    <motion.div
      className="perspective-1000 h-[220px] w-full cursor-pointer"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: index * 0.1 }}
      onClick={() => setIsFlipped(!isFlipped)}
      style={{
        perspective: "1000px",
      }}
    >
      <motion.div
        className="relative h-full w-full"
        animate={{ rotateY: isFlipped ? 180 : 0 }}
        transition={{
          duration: 0.6,
          type: "spring",
          stiffness: 300,
          damping: 30,
        }}
        style={{
          transformStyle: "preserve-3d",
        }}
      >
        {/* Front of card */}
        <motion.div
          className={`absolute inset-0 flex flex-col rounded-xl border ${getCategoryBorderColor(skill.category)} bg-gradient-to-br ${getCategoryGradient(skill.category)} p-6 shadow-lg backdrop-blur-sm`}
          style={{
            rotateX,
            rotateY,
            backfaceVisibility: "hidden",
          }}
          onMouseMove={handleMouseMove}
          onMouseLeave={handleMouseLeave}
          whileHover={{ scale: 1.02 }}
        >
          {/* Spotlight effect */}
          <motion.div
            className="pointer-events-none absolute inset-0 rounded-xl opacity-0 mix-blend-soft-light"
            style={{
              background:
                "radial-gradient(circle at 0% 0%, rgba(255,255,255,0.8) 0%, transparent 70%)",
              top: y,
              left: x,
              translateX: "-50%",
              translateY: "-50%",
            }}
            animate={{ opacity: 0.7 }}
          />

          <div className="flex items-center gap-4">
            <div
              className={`flex h-14 w-14 items-center justify-center rounded-lg bg-white/10 shadow-inner backdrop-blur-md ${getCategoryTextColor(skill.category)}`}
            >
              {skill.icon}
            </div>
            <h3 className="font-heading text-xl font-bold">{skill.name}</h3>
          </div>

          {/* Skill level bar */}
          <div className="mt-auto">
            <div className="mb-2 flex items-center justify-between">
              <span className="text-xs font-medium">Proficiency</span>
              <span
                className={`text-xs font-bold ${getCategoryTextColor(skill.category)}`}
              >
                {skill.proficiency}%
              </span>
            </div>
            <div className="h-2 w-full overflow-hidden rounded-full bg-black/10">
              <motion.div
                className={`h-full rounded-full ${getProgressBarColor(skill.category)}`}
                initial={{ width: 0 }}
                animate={{ width: `${skill.proficiency}%` }}
                transition={{ duration: 1, delay: 0.2 }}
              />
            </div>
          </div>

          <div className="mt-4 flex items-center justify-between">
            <div className="text-xs font-medium tracking-wider uppercase opacity-70">
              {skill.category}
            </div>
            <div className="text-xs">Tap to flip</div>
          </div>
        </motion.div>

        {/* Back of card */}
        <motion.div
          className={`absolute inset-0 flex flex-col rounded-xl border ${getCategoryBorderColor(skill.category)} bg-gradient-to-br ${getCategoryGradient(skill.category)} p-6 shadow-lg backdrop-blur-sm`}
          style={{
            rotateX,
            backfaceVisibility: "hidden",
            rotateY: 180,
          }}
          onMouseMove={handleMouseMove}
          onMouseLeave={handleMouseLeave}
          whileHover={{ scale: 1.02 }}
        >
          <h3
            className={`font-heading mb-2 text-lg font-bold ${getCategoryTextColor(skill.category)}`}
          >
            {skill.name}
          </h3>

          <p className="text-muted-foreground flex-1 text-sm">
            {skill.description || "No description available."}
          </p>

          <div className="mt-4 flex items-center justify-between">
            <div className="text-xs font-medium tracking-wider uppercase opacity-70">
              {skill.category}
            </div>
            <div className="text-xs">Tap to flip back</div>
          </div>
        </motion.div>
      </motion.div>
    </motion.div>
  );
}
