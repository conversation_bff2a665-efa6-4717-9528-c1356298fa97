"use client";

import GradientText from "@/components/effects/GradientText";
import { Button } from "@/components/ui/button";
import { motion, useInView } from "framer-motion";
import {
  ArrowRight,
  Clock,
  Code2,
  Download,
  Lightbulb,
  Smartphone,
  Wrench,
} from "lucide-react";
import Link from "next/link";
import { useRef } from "react";

export default function About() {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });
  const experienceYears = new Date().getFullYear() - 2022;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  const highlightVariants = {
    hidden: { opacity: 0, y: 10 },
    visible: (i: number) => ({
      opacity: 1,
      y: 0,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
        ease: "easeOut",
      },
    }),
  };

  const coreStrengths = [
    {
      title: "Experience",
      description: `${experienceYears}+ years of frontend development`,
      icon: <Clock className="h-5 w-5" />,
    },
    {
      title: "React & Next.js Mastery",
      description:
        "Deep expertise in React, Next.js, and the modern JS/TS stack",
      icon: <Code2 className="h-5 w-5" />,
    },
    {
      title: "User-Centric Design",
      description: "Focus on responsive, mobile-first, and accessible UIs",
      icon: <Smartphone className="h-5 w-5" />,
    },
    {
      title: "Modern Development Practices",
      icon: <Wrench className="h-5 w-5" />,
      description:
        "Proficient in agile methodologies, Git, and CI/CD workflows",
    },
  ];

  return (
    <section
      id="about"
      ref={sectionRef}
      className="relative overflow-hidden py-24 sm:py-32"
    >
      {/* Background elements */}
      <div className="absolute inset-0 -z-10 opacity-5">
        <div className="absolute top-0 left-0 h-64 w-64 rounded-full bg-purple-500/30 blur-3xl" />
        <div className="absolute right-0 bottom-0 h-64 w-64 rounded-full bg-blue-500/30 blur-3xl" />
      </div>

      <div className="container">
        <motion.div
          className="mb-16 space-y-4 text-center"
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
        >
          <motion.span
            className="bg-primary/10 text-primary mb-2 inline-block rounded-full px-4 py-1.5 text-sm font-medium"
            variants={itemVariants}
          >
            About Me
          </motion.span>
          <motion.h2
            className="font-heading text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl"
            variants={itemVariants}
          >
            <GradientText>Get to Know Me Better</GradientText>
          </motion.h2>
          <motion.p
            className="text-muted-foreground mx-auto max-w-[42rem] text-lg"
            variants={itemVariants}
          >
            Passionate frontend developer crafting exceptional web experiences
          </motion.p>
        </motion.div>

        <div className="mx-auto max-w-6xl">
          <motion.div
            className="mb-16 grid grid-cols-1 gap-8 md:grid-cols-2 lg:gap-12"
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            variants={containerVariants}
          >
            {/* Left column - Profile image */}
            <motion.div
              className="relative flex items-center justify-center"
              variants={itemVariants}
            >
              <motion.div
                className="relative"
                whileHover={{ scale: 1.03 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                {/* Decorative elements */}
                <div className="absolute -top-6 -left-6 h-24 w-24 rounded-full border-2 border-dashed border-purple-500/30" />
                <div className="absolute -right-6 -bottom-6 h-24 w-24 rounded-full border-2 border-dashed border-blue-500/30" />

                {/* Content container */}
                <div className="relative z-10 h-[350px] w-[350px] overflow-hidden rounded-2xl bg-gradient-to-br from-purple-500/20 via-blue-500/20 to-pink-500/20 p-1">
                  <div className="bg-background/80 h-full w-full overflow-hidden rounded-xl p-6 backdrop-blur-sm">
                    <div className="flex h-full flex-col justify-between">
                      {/* Tech Stack Icons */}
                      <div className="grid grid-cols-3 gap-4">
                        <div className="flex flex-col items-center gap-2">
                          <div className="bg-primary/10 text-primary flex h-12 w-12 items-center justify-center rounded-xl">
                            <Code2 className="h-6 w-6" />
                          </div>
                          <span className="text-xs font-medium">React</span>
                        </div>
                        <div className="flex flex-col items-center gap-2">
                          <div className="bg-primary/10 text-primary flex h-12 w-12 items-center justify-center rounded-xl">
                            <Smartphone className="h-6 w-6" />
                          </div>
                          <span className="text-xs font-medium">Next.js</span>
                        </div>
                        <div className="flex flex-col items-center gap-2">
                          <div className="bg-primary/10 text-primary flex h-12 w-12 items-center justify-center rounded-xl">
                            <Wrench className="h-6 w-6" />
                          </div>
                          <span className="text-xs font-medium">
                            TypeScript
                          </span>
                        </div>
                      </div>

                      {/* Stats */}
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-primary/5 rounded-lg p-3 text-center">
                          <div className="text-primary text-2xl font-bold">
                            {experienceYears}+
                          </div>
                          <div className="text-muted-foreground text-xs">
                            Years Experience
                          </div>
                        </div>
                        <div className="bg-primary/5 rounded-lg p-3 text-center">
                          <div className="text-primary text-2xl font-bold">
                            50+
                          </div>
                          <div className="text-muted-foreground text-xs">
                            Projects Completed
                          </div>
                        </div>
                      </div>

                      {/* Quote */}
                      <div className="text-center">
                        <Lightbulb className="text-primary mx-auto mb-2 h-6 w-6" />
                        <p className="text-muted-foreground text-sm">
                          Building the future of web experiences, one pixel at a
                          time
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Experience badge */}
                <motion.div
                  className="bg-background border-border absolute -right-4 bottom-8 rounded-full border p-3 shadow-lg"
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  transition={{ delay: 0.5, duration: 0.5, type: "spring" }}
                >
                  <div className="flex items-center gap-2">
                    <span className="text-primary text-xl font-bold">
                      {experienceYears}+
                    </span>
                    <span className="text-xs font-medium">
                      Years
                      <br />
                      Experience
                    </span>
                  </div>
                </motion.div>
              </motion.div>
            </motion.div>

            {/* Right column - About content */}
            <motion.div
              className="flex flex-col justify-center"
              variants={containerVariants}
            >
              <motion.div className="space-y-6" variants={containerVariants}>
                <motion.h3
                  className="font-heading text-2xl font-bold"
                  variants={itemVariants}
                >
                  Frontend Developer with a passion for creating exceptional
                  user experiences
                </motion.h3>

                <motion.div className="space-y-4" variants={containerVariants}>
                  <motion.p
                    className="text-muted-foreground leading-relaxed"
                    variants={itemVariants}
                  >
                    With {experienceYears}+ years dedicated to frontend
                    development, I have honed my skills in the React ecosystem
                    and modern JavaScript, transforming complex problems into
                    elegant and performant web solutions.
                  </motion.p>

                  <motion.p
                    className="text-muted-foreground leading-relaxed"
                    variants={itemVariants}
                  >
                    My passion lies in the intersection of design and
                    technology, creating interfaces that are not only visually
                    appealing but also intuitive and accessible. I believe in
                    continuous learning and staying ahead of the curve with
                    emerging web technologies and best practices.
                  </motion.p>
                </motion.div>

                <motion.div
                  className="flex flex-wrap gap-4"
                  variants={itemVariants}
                >
                  <Link href="/projects">
                    <Button variant="outline" className="gap-2 rounded-full">
                      View My Work
                      <ArrowRight className="h-4 w-4" />
                    </Button>
                  </Link>

                  <Link
                    href="/Mohamed_Zaki_Front_End_Developer_resume.pdf"
                    target="_blank"
                  >
                    <Button variant="ghost" className="gap-2 rounded-full">
                      Download Resume
                      <Download className="h-4 w-4" />
                    </Button>
                  </Link>
                </motion.div>
              </motion.div>
            </motion.div>
          </motion.div>

          {/* Core strengths section */}
          <motion.div
            className="space-y-8"
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            variants={containerVariants}
          >
            <motion.div className="text-center" variants={itemVariants}>
              <h3 className="font-heading mb-2 text-2xl font-bold">
                Core Strengths
              </h3>
              <p className="text-muted-foreground mx-auto max-w-2xl">
                Key skills and expertise that define my approach to frontend
                development
              </p>
            </motion.div>

            <motion.div
              className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4"
              variants={containerVariants}
            >
              {coreStrengths.map((strength, index) => (
                <motion.div
                  key={strength.title}
                  className="group border-border from-background to-secondary/10 hover:border-primary/20 relative overflow-hidden rounded-xl border bg-gradient-to-br p-6 transition-all hover:shadow-lg"
                  variants={highlightVariants}
                  custom={index}
                  whileHover={{ y: -5 }}
                  transition={{ type: "spring", stiffness: 400 }}
                >
                  <div className="bg-primary/5 absolute -top-6 -right-6 h-24 w-24 rounded-full opacity-0 transition-opacity duration-300 group-hover:opacity-100" />

                  <div className="relative z-10">
                    <div className="bg-primary/10 text-primary mb-4 inline-flex rounded-lg p-3">
                      {strength.icon}
                    </div>

                    <h4 className="mb-2 text-lg font-semibold">
                      {strength.title}
                    </h4>

                    <p className="text-muted-foreground text-sm">
                      {strength.description}
                    </p>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>

          {/* Philosophy section */}
          <motion.div
            className="via-background mt-16 rounded-2xl bg-gradient-to-br from-purple-500/5 to-blue-500/5 p-8 sm:p-10"
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            variants={containerVariants}
          >
            <motion.div
              className="flex flex-col gap-8 md:flex-row md:items-center"
              variants={containerVariants}
            >
              <motion.div className="md:w-1/4" variants={itemVariants}>
                <div className="bg-primary/10 text-primary mx-auto flex h-20 w-20 items-center justify-center rounded-full">
                  <Lightbulb className="h-10 w-10" />
                </div>
              </motion.div>

              <motion.div className="md:w-3/4" variants={itemVariants}>
                <h3 className="font-heading mb-4 text-center text-2xl font-bold md:text-left">
                  My Development Philosophy
                </h3>
                <p className="text-muted-foreground mb-4 leading-relaxed">
                  I approach each project with a commitment to excellence,
                  focusing on clean code, performance optimization, and
                  user-centered design. My goal is to create web applications
                  that not only meet but exceed expectations, delivering value
                  through intuitive interfaces and seamless functionality.
                </p>
                <p className="text-muted-foreground leading-relaxed">
                  By staying current with industry trends and best practices, I
                  ensure that my work remains at the cutting edge of web
                  development, providing clients and users with modern,
                  efficient, and delightful digital experiences.
                </p>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
