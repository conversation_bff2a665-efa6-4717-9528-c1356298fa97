import { ReactNode } from "react";

export interface Experience {
  period: string;
  startDate: Date;
  role: string;
  company: string;
  description: string;
  achievements?: string[];
  technologies?: string[];
  location?: string;
}

export interface Project {
  title: string;
  description: string;
  tech: string[];
  github: string;
  demo: string;
}

export interface Skill {
  name: string;
  icon: ReactNode;
  proficiency: number; // 0-100 scale
  category: string;
  description?: string;
}

export interface Education {
  degree: string;
  school: string;
  period: string;
  grade: string;
  description?: string;
}

export interface Certification {
  title: string;
  issuer: string;
  date: string;
  credentialId?: string;
  credentialURL?: string;
  icon?: ReactNode;
}
