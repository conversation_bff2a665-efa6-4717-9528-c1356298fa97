"use client";

import GradientText from "@/components/effects/GradientText";
import { Button } from "@/components/ui/button";
import { URLS } from "@/utils/urls";
import { motion, useInView } from "framer-motion";
import { ArrowUpRight, Mail, MessageSquare, Send } from "lucide-react";
import Link from "next/link";
import { useRef } from "react";
import { FaGithub, FaLinkedin, FaWhatsapp } from "react-icons/fa";

export default function Contact() {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  const cardVariants = {
    hidden: { y: 20, opacity: 0, scale: 0.95 },
    visible: (i: number) => ({
      y: 0,
      opacity: 1,
      scale: 1,
      transition: {
        delay: i * 0.1,
        duration: 0.5,
        type: "spring",
        stiffness: 100,
      },
    }),
  };

  const contacts = [
    {
      title: "LinkedIn",
      icon: <FaLinkedin className="h-6 w-6" />,
      href: URLS.linkedin,
      username: "Mohamed Zaki",
      color: "from-blue-500 to-blue-600",
      description: "Connect with me professionally",
    },
    {
      title: "Email",
      icon: <Mail className="h-6 w-6" />,
      href: URLS.email,
      username: "<EMAIL>",
      color: "from-red-500 to-orange-500",
      description: "Send me an email anytime",
    },
    {
      title: "WhatsApp",
      icon: <FaWhatsapp className="h-6 w-6" />,
      href: URLS.whatsapp,
      username: "+201027286456",
      color: "from-green-500 to-emerald-500",
      description: "Quick chat and responses",
    },
    {
      title: "GitHub",
      icon: <FaGithub className="h-6 w-6" />,
      href: URLS.github,
      username: "Mohamed-A-Zaki",
      color: "from-gray-600 to-gray-700",
      description: "Check out my code repositories",
    },
  ];

  return (
    <section
      id="contact"
      className="relative overflow-hidden py-24 sm:py-32"
      ref={sectionRef}
    >
      {/* Background elements */}
      <div className="absolute inset-0 -z-10">
        <div className="via-background absolute inset-0 bg-gradient-to-br from-purple-500/5 to-blue-500/5" />
      </div>

      {/* Decorative elements */}
      <div className="pointer-events-none absolute -bottom-16 -left-16 h-64 w-64 rounded-full bg-purple-500/5 blur-3xl" />
      <div className="pointer-events-none absolute -top-16 -right-16 h-64 w-64 rounded-full bg-blue-500/5 blur-3xl" />

      <div className="relative container">
        <motion.div
          className="mb-16 space-y-4 text-center"
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
        >
          <motion.span
            className="bg-primary/10 text-primary mb-2 inline-block rounded-full px-4 py-1.5 text-sm font-medium"
            variants={itemVariants}
          >
            Contact
          </motion.span>
          <motion.h2
            className="font-heading text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl"
            variants={itemVariants}
          >
            <GradientText>Get in Touch</GradientText>
          </motion.h2>
          <motion.p
            className="text-muted-foreground mx-auto max-w-[42rem] text-lg"
            variants={itemVariants}
          >
            Feel free to reach out for collaborations or just a friendly hello
          </motion.p>
        </motion.div>

        <div className="mx-auto max-w-5xl">
          <motion.div
            className="mb-16 grid grid-cols-1 gap-6 sm:grid-cols-2"
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            variants={containerVariants}
          >
            {contacts.map((contact, index) => (
              <motion.div
                key={contact.title}
                custom={index}
                variants={cardVariants}
              >
                <Link href={contact.href} target="_blank">
                  <motion.div
                    whileHover={{ y: -5, scale: 1.02 }}
                    transition={{ type: "spring", stiffness: 300 }}
                    className="group border-border from-background via-background to-background hover:border-primary/20 relative h-full overflow-hidden rounded-xl border bg-gradient-to-br p-1 transition-all duration-300 hover:shadow-lg"
                  >
                    {/* Gradient border effect */}
                    <div
                      className={`absolute inset-0 bg-gradient-to-r ${contact.color} opacity-0 blur-xl transition-opacity duration-300 group-hover:opacity-20`}
                    />

                    <div className="bg-background/80 relative h-full rounded-lg p-6 backdrop-blur-sm">
                      <div className="flex items-center gap-4">
                        <div
                          className={`bg-gradient-to-br ${contact.color} flex h-14 w-14 items-center justify-center rounded-xl text-white`}
                        >
                          {contact.icon}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h3 className="font-heading text-xl font-semibold">
                              {contact.title}
                            </h3>
                            <motion.div
                              className="text-primary rounded-full p-1 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                              whileHover={{ rotate: 45 }}
                            >
                              <ArrowUpRight className="h-4 w-4" />
                            </motion.div>
                          </div>
                          <p className="text-muted-foreground text-sm">
                            {contact.username}
                          </p>
                        </div>
                      </div>
                      <p className="text-muted-foreground mt-4 text-sm">
                        {contact.description}
                      </p>
                    </div>
                  </motion.div>
                </Link>
              </motion.div>
            ))}
          </motion.div>

          {/* Call to action */}
          <motion.div
            className="via-background mx-auto max-w-2xl rounded-2xl bg-gradient-to-br from-purple-500/10 to-blue-500/10 p-8 text-center sm:p-10"
            initial="hidden"
            animate={isInView ? "visible" : "hidden"}
            variants={containerVariants}
          >
            <motion.div className="space-y-6" variants={containerVariants}>
              <motion.div variants={itemVariants}>
                <MessageSquare className="text-primary mx-auto mb-4 h-10 w-10" />
              </motion.div>

              <motion.h3
                className="font-heading text-2xl font-bold"
                variants={itemVariants}
              >
                Let&apos;s Discuss Your Project
              </motion.h3>

              <motion.p
                className="text-muted-foreground mx-auto max-w-lg"
                variants={itemVariants}
              >
                Have a project in mind or want to explore collaboration
                opportunities? I&apos;m always open to discussing new ideas and
                challenges.
              </motion.p>

              <motion.div variants={itemVariants}>
                <Link href={URLS.whatsapp} target="_blank">
                  <Button
                    size="lg"
                    className="hover:shadow-primary/20 gap-2 rounded-full px-8 shadow-lg transition-all"
                  >
                    <Send className="h-4 w-4" />
                    Send a Message
                  </Button>
                </Link>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  );
}
