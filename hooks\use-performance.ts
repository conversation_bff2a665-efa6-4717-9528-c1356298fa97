"use client";

import { useEffect, useState } from "react";

// Define extended Navigator interface with performance-related properties
interface ExtendedNavigator extends Navigator {
  deviceMemory?: number;
  connection?: {
    effectiveType?: string;
    downlink?: number;
    rtt?: number;
    saveData?: boolean;
  };
}

interface PerformanceProfile {
  isLowEndDevice: boolean;
  isLowEndExperience: boolean;
  prefersReducedMotion: boolean;
  connection: "slow" | "medium" | "fast" | "unknown";
}

// Default profile for SSR or when browser APIs are not available
const defaultProfile: PerformanceProfile = {
  isLowEndDevice: false,
  isLowEndExperience: false,
  prefersReducedMotion: false,
  connection: "unknown",
};

/**
 * Hook to detect device performance capabilities and user preferences
 * to optimize animations and effects
 */
export function usePerformance(): PerformanceProfile {
  const [profile, setProfile] = useState<PerformanceProfile>(defaultProfile);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    // Mark component as mounted
    setIsMounted(true);

    // Skip if not in browser environment
    if (typeof window === "undefined" || typeof navigator === "undefined") {
      return;
    }

    try {
      // Check for reduced motion preference
      const prefersReducedMotion = window.matchMedia(
        "(prefers-reduced-motion: reduce)",
      ).matches;

      // Cast navigator to our extended type
      const extNav = navigator as ExtendedNavigator;

      // Check for device memory (only available in Chrome)
      const memory = extNav.deviceMemory ?? 4;
      const isLowMemoryDevice = memory < 4;

      // Check for hardware concurrency (CPU cores)
      const cores = navigator.hardwareConcurrency ?? 4;
      const isLowCoreDevice = cores < 4;

      // Check connection type if available
      let connectionType: "slow" | "medium" | "fast" | "unknown" = "unknown";

      if (extNav.connection) {
        const { effectiveType, downlink } = extNav.connection;

        if (
          effectiveType === "slow-2g" ||
          effectiveType === "2g" ||
          (downlink !== undefined && downlink < 0.5)
        ) {
          connectionType = "slow";
        } else if (
          effectiveType === "3g" ||
          (downlink !== undefined && downlink < 2)
        ) {
          connectionType = "medium";
        } else if (effectiveType || downlink !== undefined) {
          connectionType = "fast";
        }
      }

      // Determine if this is a low-end device
      const isLowEndDevice = isLowMemoryDevice || isLowCoreDevice;

      // Determine if we should provide a low-end experience
      const isLowEndExperience =
        isLowEndDevice || prefersReducedMotion || connectionType === "slow";

      // Update the profile
      setProfile({
        isLowEndDevice,
        isLowEndExperience,
        prefersReducedMotion,
        connection: connectionType,
      });
    } catch (error) {
      // Fallback to default profile if any errors occur
      console.error("Error detecting performance capabilities:", error);
    }
  }, []);

  // Return default profile until mounted to prevent hydration mismatch
  return isMounted ? profile : defaultProfile;
}
