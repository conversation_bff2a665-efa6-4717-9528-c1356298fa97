import {
  Certification,
  type Experience,
  type Project,
  type Skill,
} from "@/types";
import {
  Brain,
  Clock,
  MessagesSquare,
  Puzzle,
  Users,
  Workflow,
} from "lucide-react";
import {
  SiCss3,
  SiFigma,
  SiGit,
  SiHtml5,
  SiJavascript,
  SiNextdotjs,
  SiReact,
  SiRedux,
  SiTailwindcss,
  SiTypescript,
  SiVuedotjs,
} from "react-icons/si";
import { TbBrandFramerMotion } from "react-icons/tb";

// Combined skills array for a unified display
export const skills: Skill[] = [
  // Frontend Core
  {
    name: "React",
    icon: <SiReact className="h-8 w-8" />,
    proficiency: 90,
    category: "Frontend",
    description:
      "Building interactive UIs with React's component-based architecture",
  },
  {
    name: "TypeScript",
    icon: <SiTypescript className="h-8 w-8" />,
    proficiency: 85,
    category: "Frontend",
    description:
      "Developing type-safe applications with enhanced maintainability",
  },
  {
    name: "JavaScript",
    icon: <SiJavascript className="h-8 w-8" />,
    proficiency: 92,
    category: "Frontend",
    description: "Creating dynamic web experiences with modern JavaScript",
  },
  {
    name: "Next.js",
    icon: <SiNextdotjs className="h-8 w-8" />,
    proficiency: 80,
    category: "Frontend",
    description: "Building performant and SEO-friendly React applications",
  },
  {
    name: "HTML5",
    icon: <SiHtml5 className="h-8 w-8" />,
    proficiency: 95,
    category: "Frontend",
    description: "Crafting semantic and accessible markup",
  },
  {
    name: "CSS3",
    icon: <SiCss3 className="h-8 w-8" />,
    proficiency: 90,
    category: "Frontend",
    description: "Styling web applications with modern CSS techniques",
  },
  {
    name: "Tailwind CSS",
    icon: <SiTailwindcss className="h-8 w-8" />,
    proficiency: 95,
    category: "Frontend",
    description: "Rapidly building custom designs with utility-first approach",
  },
  {
    name: "Framer Motion",
    icon: <TbBrandFramerMotion className="h-8 w-8" />,
    proficiency: 85,
    category: "Frontend",
    description: "Creating fluid animations and interactive UI elements",
  },
  {
    name: "Redux",
    icon: <SiRedux className="h-8 w-8" />,
    proficiency: 85,
    category: "Frontend",
    description:
      "Managing complex application state with predictable state containers",
  },
  {
    name: "Vue.js",
    icon: <SiVuedotjs className="h-8 w-8" />,
    proficiency: 82,
    category: "Frontend",
    description:
      "Building progressive web interfaces with Vue's reactive component system",
  },

  // Backend & APIs
  // {
  //   name: "Node.js",
  //   icon: <SiNodedotjs className="h-8 w-8" />,
  //   proficiency: 75,
  //   category: "Backend",
  //   description: "Building scalable server-side applications with JavaScript",
  // },
  // {
  //   name: "REST APIs",
  //   icon: <TbApi className="h-8 w-8" />,
  //   proficiency: 80,
  //   category: "Backend",
  //   description:
  //     "Designing and consuming RESTful services for web applications",
  // },
  // {
  //   name: "MongoDB",
  //   icon: <SiMongodb className="h-8 w-8" />,
  //   proficiency: 70,
  //   category: "Backend",
  //   description: "Working with NoSQL databases for flexible data storage",
  // },
  // Development Tools
  {
    name: "Git & GitHub",
    icon: <SiGit className="h-8 w-8" />,
    proficiency: 85,
    category: "Tools",
    description: "Version control and collaborative development workflows",
  },
  {
    name: "Figma",
    icon: <SiFigma className="h-8 w-8" />,
    proficiency: 80,
    category: "Tools",
    description: "Collaborating on design and implementing UI from mockups",
  },

  // Soft Skills
  {
    name: "Problem Solving",
    icon: <Puzzle className="h-8 w-8" />,
    proficiency: 90,
    category: "Soft Skills",
    description: "Breaking down complex problems into manageable solutions",
  },
  {
    name: "Team Collaboration",
    icon: <Users className="h-8 w-8" />,
    proficiency: 85,
    category: "Soft Skills",
    description: "Working effectively in cross-functional teams",
  },
  {
    name: "Communication",
    icon: <MessagesSquare className="h-8 w-8" />,
    proficiency: 90,
    category: "Soft Skills",
    description: "Clearly conveying technical concepts to diverse audiences",
  },
  {
    name: "Time Management",
    icon: <Clock className="h-8 w-8" />,
    proficiency: 80,
    category: "Soft Skills",
    description: "Prioritizing tasks and meeting deadlines efficiently",
  },
  {
    name: "Critical Thinking",
    icon: <Brain className="h-8 w-8" />,
    proficiency: 85,
    category: "Soft Skills",
    description: "Evaluating situations objectively to make informed decisions",
  },
  {
    name: "Adaptability",
    icon: <Workflow className="h-8 w-8" />,
    proficiency: 85,
    category: "Soft Skills",
    description: "Quickly adjusting to new technologies and requirements",
  },
];

export const projects: Project[] = [
  {
    title: "wildlife-portal",
    description:
      "A centralized platform for managing and exploring wildlife data across Saudi Arabia. It provides tools for data visualization, tracking, and analysis, empowering researchers, conservationists, and policymakers. The platform fosters awareness and informed decision-making through intuitive interfaces and real-time data access.",
    tech: ["React", "TypeScript", "Tailwind"],
    github: "#",
    demo: "https://habitat.ncw.gov.sa/wildlife-portal",
  },
  {
    title: "wildlife-explorer",
    description:
      "An interactive platform developed to explore and visualize wildlife distribution across Saudi Arabia. This project includes rich data mapping, user-friendly navigation, and dynamic search capabilities to aid in research and awareness efforts.",
    tech: ["React", "TypeScript", "Tailwind"],
    github: "#",
    // demo: "https://geoservices2.syadtech.com/wildlifeexplorer",
    demo: "https://habitat.ncw.gov.sa/wildlifeexplorer",
  },
  {
    title: "Job-Nest",
    description:
      "A job portal project built with React, TypeScript, and Tailwind CSS. It features a responsive user interface, dynamic job filtering, and a job board for easy job searching.",
    tech: ["React", "TypeScript", "Tailwind", "Mantine"],
    github: "#",
    demo: "https://front-end-job-nest.vercel.app/",
  },
  {
    title: "legal-office",
    description:
      "A leading law firm in the Gulf States, we provide comprehensive legal services to our individual and corporate clients.",
    tech: ["Vue", "TypeScript", "Tailwind"],
    github: "https://github.com/Mohamed-A-Zaki/legal-office",
    demo: "https://legal-office.vercel.app/",
  },
  {
    title: "amazon-clone",
    description:
      "An Amazon clone project, built with Next.js, TypeScript, and Tailwind CSS. It features a responsive user interface, dynamic product filtering, and a cart system for easy product management.",
    tech: ["Next js", "TypeScript", "Tailwind"],
    github: "https://github.com/Mohamed-A-Zaki/amazon-clone",
    demo: "https://amazon-clone-123.vercel.app/",
  },
  {
    title: "Coffee Heaven",
    description:
      "CoffeeVue is a sleek and modern e-commerce website designed to deliver a rich coffee shopping experience. Built with Vue.js, the platform showcases a curated collection of premium coffee products with an elegant and user-friendly interface. Visitors can browse featured blends, explore product details, and enjoy a responsive design optimized for all devices. With its minimal aesthetic, smooth animations, and intuitive navigation, CoffeeVue serves as a perfect front-end showcase for developers and designers alike.",
    tech: ["Vue", "TypeScript", "Tailwind", "shadcn/ui"],
    github: "https://github.com/Mohamed-A-Zaki/coffe-vue",
    demo: "https://coffe-vue.vercel.app/",
  },
  {
    title: "jira-clone",
    description:
      "A Jira clone project built with Next.js, TypeScript, and Tailwind CSS. It features a responsive user interface, dynamic task filtering, and a board system for easy project management.",
    tech: ["Next js", "TypeScript", "Tailwind", "shadcn/ui"],
    github: "https://github.com/Mohamed-A-Zaki/jira-clone-front-end-nextjs",
    demo: "https://jira-clone-front-end-nextjs.vercel.app/auth/sign-in",
  },
  {
    title: "Geo-UI",
    description:
      "Geo Explorer is a modern, interactive web application designed to visualize and explore the geographic distribution of wildlife across Saudi Arabia. Built with a clean and responsive front-end, the platform offers users a user-friendly interface to interact with data-rich maps, species information, and ecological zones.",
    tech: ["React", "TypeScript", "Tailwind", "shadcn/ui"],
    github: "#",
    demo: "https://geo-ui-six.vercel.app/",
  },
  {
    title: "medicare",
    description:
      "A healthcare platform built with Next.js, TypeScript, and Tailwind CSS. It features a responsive user interface, dynamic product filtering, and a cart system for easy product management.",
    tech: ["Next js", "TypeScript", "Tailwind", "Mantine"],
    github: "https://github.com/Mohamed-A-Zaki/medicare-frontend",
    demo: "https://medicare-frontend-coral.vercel.app/",
  },
  {
    title: "Digital Design",
    description:
      "Digital Design is a Multipage and Multipurpose Business Agency. This template for a business who are doing business as business agency, corporate, creative agency, portfolio, digital marketing, startups and related products / services. Build with React and Bootstrap framework.",
    tech: ["React", "Javascript"],
    github: "#",
    demo: "https://digital-design-2.vercel.app/",
  },
];

export const experiences: Experience[] = [
  {
    period: "April 2024 - Present",
    startDate: new Date(2024, 3), // April 2024
    role: "Frontend Developer",
    company: "Syad for Communications & IT",
    location: "Riyadh, Saudi Arabia",
    description:
      "Building and maintaining web applications using modern frameworks like React. Converting UI/UX designs into interactive, responsive, and accessible interfaces. Optimizing performance and ensuring a seamless user experience. Integrating front-end components with backend APIs. Managing state efficiently using Redux Toolkit. Adhering to best coding practices by writing clean, maintainable code. Collaborating with cross-functional teams in agile environments. Delivering high-quality products and troubleshooting issues. Continuously improving the applications I work on.",
    achievements: [
      "Led the development of the Wildlife Explorer platform, improving data visualization performance by 40%",
      "Implemented responsive design patterns that reduced mobile bounce rates by 25%",
      "Optimized application load time by 30% through code splitting and lazy loading techniques",
      "Collaborated with UX team to redesign key user flows, resulting in 20% increase in user engagement",
    ],
    technologies: [
      "React",
      "TypeScript",
      "Next.js",
      "Tailwind CSS",
      "State Management",
      "Framer Motion",
    ],
  },
  {
    period: "January 2022 - Present",
    startDate: new Date(2022, 0), // January 2022
    role: "Frontend Developer",
    company: "Freelance",
    location: "Remote",
    description:
      "Specializing in React development for web applications. Designing and implementing intuitive, user-friendly interfaces. Combining strong visual design skills with front-end development expertise. Creating responsive layouts for optimal viewing on all devices. Implementing modern JavaScript features and best practices. Collaborating with clients to understand project requirements. Delivering high-quality code that meets client expectations.",
    achievements: [
      "Delivered 15+ successful web applications for clients across various industries",
      "Maintained 100% client satisfaction rate with on-time project delivery",
      "Implemented SEO best practices resulting in improved search rankings for client websites",
      "Developed custom component libraries to accelerate development time by 30%",
    ],
    technologies: [
      "React",
      "typescript",
      "Next",
      "Tailwind CSS",
      "State Management",
      "Framer Motion",
    ],
  },
];

export const certifications: Certification[] = [
  {
    title: "React - The Complete Guide",
    issuer: "Udemy",
    date: "June 2022",
    credentialId: "UC-DEF345678",
    credentialURL: "https://www.udemy.com/certificate/UC-DEF345678/",
  },
  {
    title: "TypeScript for JavaScript Developers",
    issuer: "Udemy",
    date: "September 2022",
    credentialId: "UC-GHI901234",
    credentialURL: "https://www.udemy.com/certificate/UC-GHI901234/",
  },
];
