import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Tool } from "@/data/tools";
import { motion } from "framer-motion";
import { CheckCircle2, ExternalLink, Github } from "lucide-react";

interface ToolCardProps {
  tool: Tool;
}

export function ToolCard({ tool }: ToolCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -5 }}
      transition={{ type: "spring", stiffness: 300 }}
    >
      <a
        href={tool.website}
        target="_blank"
        rel="noopener noreferrer"
        className="block h-full"
      >
        <Card className="group hover:border-primary/50 relative h-full overflow-hidden border-2 transition-all duration-300 hover:shadow-lg">
          <div className="from-primary/5 absolute inset-0 bg-gradient-to-br via-transparent to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-primary/10 text-primary rounded-xl p-2.5 transition-transform duration-300 group-hover:scale-110">
                  {tool.icon}
                </div>
                <div>
                  <CardTitle className="text-xl font-semibold tracking-tight">
                    {tool.name}
                  </CardTitle>
                  <CardDescription className="mt-1 line-clamp-2">
                    {tool.description}
                  </CardDescription>
                </div>
              </div>
              <div className="flex gap-2">
                {tool.documentation && (
                  <a
                    href={tool.documentation}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary transition-colors"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </a>
                )}
                {tool.github && (
                  <a
                    href={tool.github}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-muted-foreground hover:text-primary transition-colors"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Github className="h-4 w-4" />
                  </a>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex flex-wrap gap-2">
                {tool.tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="bg-secondary/50 text-secondary-foreground hover:bg-secondary/70 transition-colors duration-300"
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
              {tool.features && (
                <div className="space-y-2">
                  <h4 className="text-muted-foreground text-sm font-medium">
                    Key Features
                  </h4>
                  <ul className="space-y-1.5 text-sm">
                    {tool.features.map((feature) => (
                      <motion.li
                        key={feature}
                        className="text-muted-foreground flex items-center gap-2"
                        whileHover={{ x: 4 }}
                        transition={{ type: "spring", stiffness: 300 }}
                      >
                        <CheckCircle2 className="text-primary h-4 w-4 flex-shrink-0" />
                        <span className="line-clamp-1">{feature}</span>
                      </motion.li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </a>
    </motion.div>
  );
}
