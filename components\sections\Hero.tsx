"use client";

import GradientText from "@/components/effects/GradientText";
import TypewriterEffect from "@/components/effects/TypewriterEffect";
import { Button } from "@/components/ui/button";
import { usePerformance } from "@/hooks/use-performance";
import { URLS } from "@/utils/urls";
import { motion, useReducedMotion } from "framer-motion";
import { ArrowRight, Code, Download } from "lucide-react";
// import dynamic from "next/dynamic";
import Link from "next/link";
import { useMemo, useState } from "react";
import { FaGithub, FaLinkedin, FaWhatsapp } from "react-icons/fa";
import { MdEmail } from "react-icons/md";

// Dynamically import the CodeSnippet component
// const CodeSnippet = dynamic(() => import("../effects/CodeSnippet"), {
//   ssr: false,
//   loading: () => (
//     <div className="bg-primary/5 h-[400px] w-full animate-pulse rounded-lg"></div>
//   ),
// });

export default function Hero() {
  const [nameComplete, setNameComplete] = useState(false);
  const experienceYears = new Date().getFullYear() - 2022;
  const prefersReducedMotion = useReducedMotion();
  const { isLowEndExperience } = usePerformance();

  // Optimize animations based on device capabilities
  const optimizedAnimations = useMemo(() => {
    return {
      // Disable staggering on low-end devices
      containerStagger: isLowEndExperience ? 0 : 0.2,
      // Reduce animation complexity on low-end devices
      floatingIconsEnabled: !isLowEndExperience && !prefersReducedMotion,
      // Simplify spring animations on low-end devices
      springConfig: isLowEndExperience
        ? { type: "tween", duration: 0.3 }
        : { type: "spring", stiffness: 100, damping: 10 },
    };
  }, [isLowEndExperience, prefersReducedMotion]);

  // Animation variants - optimized based on device capabilities
  const containerVariants = useMemo(
    () => ({
      hidden: { opacity: 0 },
      visible: {
        opacity: 1,
        transition: {
          staggerChildren: optimizedAnimations.containerStagger,
          delayChildren: 0.3,
        },
      },
    }),
    [optimizedAnimations.containerStagger],
  );

  const itemVariants = useMemo(
    () => ({
      hidden: { y: 20, opacity: 0 },
      visible: {
        y: 0,
        opacity: 1,
        transition: optimizedAnimations.springConfig,
      },
    }),
    [optimizedAnimations.springConfig],
  );

  const socialVariants = {
    hidden: { scale: 0, opacity: 0 },
    visible: (i: number) => ({
      scale: 1,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 200,
        delay: 1.2 + i * 0.1,
      },
    }),
  };

  // const floatingIcons = [
  //   { icon: "/images/react.svg", top: "15%", left: "10%", size: 40, delay: 0 },
  //   {
  //     icon: "/images/nextjs.svg",
  //     top: "25%",
  //     right: "15%",
  //     size: 50,
  //     delay: 0.5,
  //   },
  //   {
  //     icon: "/images/typescript.svg",
  //     bottom: "20%",
  //     left: "15%",
  //     size: 45,
  //     delay: 1,
  //   },
  //   {
  //     icon: "/images/tailwind.svg",
  //     bottom: "30%",
  //     right: "10%",
  //     size: 40,
  //     delay: 1.5,
  //   },
  //   {
  //     icon: "/images/vue.svg",
  //     top: "40%",
  //     left: "20%",
  //     size: 45,
  //     delay: 1.2,
  //   },
  // ];

  const socials = [
    {
      href: URLS.github,
      icon: <FaGithub className="h-6 w-6" />,
      label: "GitHub",
    },
    {
      href: URLS.linkedin,
      icon: <FaLinkedin className="h-6 w-6" />,
      label: "LinkedIn",
    },
    {
      href: URLS.email,
      icon: <MdEmail className="h-6 w-6" />,
      label: "Email",
    },
    {
      href: URLS.whatsapp,
      icon: <FaWhatsapp className="h-6 w-6" />,
      label: "WhatsApp",
    },
  ];

  return (
    <section className="relative min-h-screen overflow-hidden pt-20">
      {/* Floating tech icons - only render on higher-end devices */}
      {/* {optimizedAnimations.floatingIconsEnabled &&
        floatingIcons.map((icon, index) => (
          <motion.div
            key={index}
            className="pointer-events-none absolute z-10 hidden opacity-70 md:block"
            style={{
              top: icon.top,
              left: icon.left,
              right: icon.right,
              bottom: icon.bottom,
            }}
            initial={{ opacity: 0, y: 20 }}
            animate={{
              opacity: 0.7,
              y: 0,
              transition: { delay: icon.delay, duration: 0.8 },
            }}
          >
            <motion.div
              animate={{ y: [0, 15, 0] }}
              transition={{
                duration: 6,
                repeat: Infinity,
                repeatType: "reverse",
                delay: index * 0.5,
              }}
            >
              <Image
                src={icon.icon}
                alt="Tech icon"
                width={icon.size}
                height={icon.size}
                loading="lazy"
                fetchPriority="low"
                decoding="async"
              />
            </motion.div>
          </motion.div>
        ))} */}

      {/* Main content */}
      <div className="container flex min-h-screen flex-col items-center justify-center">
        <motion.div
          className="grid w-full max-w-7xl grid-cols-1 items-center gap-12 lg:grid-cols-5"
          initial="hidden"
          animate="visible"
          variants={containerVariants}
        >
          {/* Left content */}
          <motion.div
            className="flex flex-col items-center text-center lg:col-span-3 lg:items-start lg:text-left"
            variants={containerVariants}
          >
            <motion.span
              className="bg-primary/10 text-primary mb-4 inline-block rounded-full px-4 py-1.5 text-sm font-medium"
              variants={itemVariants}
            >
              Hi, I&apos;m
            </motion.span>

            <motion.h1
              className="font-heading mb-4 text-5xl font-bold tracking-tight sm:text-6xl md:text-7xl"
              variants={itemVariants}
            >
              <GradientText className="mb-2 block">
                <TypewriterEffect
                  text="Mohamed Zaki"
                  speed={60}
                  onComplete={() => setNameComplete(true)}
                />
              </GradientText>
              <span className="text-foreground/90 block min-h-[1.2em] text-4xl sm:text-5xl md:text-6xl">
                {nameComplete && (
                  <TypewriterEffect text="Frontend Developer" speed={70} />
                )}
              </span>
            </motion.h1>

            <motion.p
              className="text-muted-foreground mb-8 max-w-xl text-lg leading-relaxed"
              variants={itemVariants}
            >
              A passionate Frontend Developer with {experienceYears}{" "}
              {experienceYears === 1 ? "year" : "years"} of experience,
              specializing in creating dynamic and user-friendly web
              applications with{" "}
              <span className="text-primary font-medium">React</span>,{" "}
              <span className="text-primary font-medium">Next.js</span>,{" "}
              <span className="text-primary font-medium">Vue</span>, and modern
              JavaScript technologies.
            </motion.p>

            <motion.div
              className="flex flex-wrap gap-4 sm:flex-nowrap"
              variants={itemVariants}
            >
              <Link href="/contact">
                <Button
                  size="lg"
                  className="hover:shadow-primary/20 gap-2 rounded-full px-6 shadow-lg transition-all"
                >
                  Get in Touch
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </Link>

              <Link href="/projects">
                <Button
                  variant="outline"
                  size="lg"
                  className="hover:bg-primary/5 gap-2 rounded-full border-2 px-6 transition-all"
                >
                  View Projects
                  <Code className="h-4 w-4" />
                </Button>
              </Link>

              <Link
                href="/Mohamed_Zaki_Front_End_Developer_resume.pdf"
                target="_blank"
              >
                <Button
                  variant="ghost"
                  size="lg"
                  className="gap-2 rounded-full px-6 transition-all"
                >
                  Resume
                  <Download className="h-4 w-4" />
                </Button>
              </Link>
            </motion.div>

            <motion.div
              className="mt-8 flex gap-4"
              variants={containerVariants}
            >
              {socials.map((social, index) => (
                <motion.div
                  key={social.href}
                  custom={index}
                  variants={socialVariants}
                >
                  <Link
                    href={social.href}
                    target="_blank"
                    aria-label={social.label}
                  >
                    <Button
                      variant="outline"
                      size="icon"
                      className="hover:border-primary hover:text-primary h-12 w-12 rounded-full border-2 transition-all hover:scale-110"
                    >
                      {social.icon}
                    </Button>
                  </Link>
                </motion.div>
              ))}
            </motion.div>
          </motion.div>{" "}
          {/* Right content - Animated Logo */}
          <motion.div
            className="relative hidden lg:col-span-2 lg:block"
            variants={itemVariants}
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <motion.div
              className="relative h-[450px] w-full"
              animate={{ y: [0, -15, 0], rotate: [0, 5, 0, -5, 0] }}
              transition={{
                y: {
                  duration: 6,
                  repeat: Infinity,
                  repeatType: "reverse",
                },
                rotate: {
                  duration: 12,
                  repeat: Infinity,
                  repeatType: "reverse",
                  ease: "easeInOut",
                },
              }}
            >
              <div className="absolute inset-0 animate-pulse rounded-3xl bg-gradient-to-br from-[#2563EB]/20 via-[#7C3AED]/20 to-[#F43F5E]/20 blur-xl" />
              <div className="bg-background/80 absolute inset-4 flex items-center justify-center overflow-hidden rounded-2xl p-8 backdrop-blur-sm">
                <div className="absolute inset-0 bg-[url('/images/noise.svg')] opacity-[0.03] mix-blend-overlay" />
                {/* Animated Code Symbol */}
                <motion.div
                  className="relative flex h-full w-full items-center justify-center"
                  animate={{
                    scale: [1, 1.05, 1],
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    repeatType: "reverse",
                    ease: "easeInOut",
                  }}
                >
                  {/* Code brackets with floating elements */}
                  <div className="relative font-mono text-6xl font-bold md:text-8xl">
                    {/* Left bracket */}
                    <motion.span
                      className="absolute top-0 left-0 bg-gradient-to-br from-blue-400 to-purple-600 bg-clip-text text-transparent"
                      animate={{
                        rotateY: [0, 10, 0],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        repeatType: "reverse",
                      }}
                    >
                      {"<"}
                    </motion.span>

                    {/* Center slash */}
                    <motion.span
                      className="relative mx-8 bg-gradient-to-br from-amber-400 to-orange-500 bg-clip-text text-transparent"
                      animate={{
                        rotateZ: [0, 5, 0, -5, 0],
                      }}
                      transition={{
                        duration: 4,
                        repeat: Infinity,
                        ease: "easeInOut",
                      }}
                    >
                      /
                    </motion.span>

                    {/* Right bracket */}
                    <motion.span
                      className="absolute top-0 right-0 bg-gradient-to-br from-pink-400 to-red-500 bg-clip-text text-transparent"
                      animate={{
                        rotateY: [0, -10, 0],
                      }}
                      transition={{
                        duration: 3,
                        repeat: Infinity,
                        repeatType: "reverse",
                      }}
                    >
                      {">"}
                    </motion.span>
                  </div>

                  {/* Floating code elements */}
                  <motion.div
                    className="absolute -top-4 -left-4 font-mono text-sm text-blue-400 opacity-60"
                    animate={{
                      y: [0, -10, 0],
                      opacity: [0.4, 0.8, 0.4],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: 0.5,
                    }}
                  >
                    const
                  </motion.div>

                  <motion.div
                    className="absolute -right-4 -bottom-4 font-mono text-sm text-purple-400 opacity-60"
                    animate={{
                      y: [0, 10, 0],
                      opacity: [0.4, 0.8, 0.4],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      delay: 1,
                    }}
                  >
                    {"{}"}
                  </motion.div>

                  <motion.div
                    className="absolute top-1/2 -left-8 font-mono text-xs text-green-400 opacity-50"
                    animate={{
                      x: [0, -5, 0],
                      opacity: [0.3, 0.7, 0.3],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      delay: 1.5,
                    }}
                  >
                    =&gt;
                  </motion.div>

                  <motion.div
                    className="absolute top-1/2 -right-8 font-mono text-xs text-yellow-400 opacity-50"
                    animate={{
                      x: [0, 5, 0],
                      opacity: [0.3, 0.7, 0.3],
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      delay: 2,
                    }}
                  >
                    ;
                  </motion.div>

                  {/* Glowing effect */}
                  <div className="absolute inset-0 -z-10 rounded-full bg-gradient-to-br from-[#2563EB]/10 via-[#7C3AED]/10 to-[#F43F5E]/10 blur-2xl" />
                </motion.div>
              </div>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>

      {/* Scroll indicator - only show on higher-end devices */}
      {!isLowEndExperience && (
        <motion.div
          className="absolute bottom-8 left-1/2 -translate-x-1/2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2 }}
        >
          <motion.div
            className="flex flex-col items-center"
            animate={{ y: [0, 10, 0] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            <div className="text-muted-foreground mb-2 text-sm">
              Scroll Down
            </div>
            <div className="from-muted-foreground/50 h-6 w-[1px] bg-gradient-to-b to-transparent" />
          </motion.div>
        </motion.div>
      )}
    </section>
  );
}
