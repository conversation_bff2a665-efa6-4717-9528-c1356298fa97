"use client";

import { motion } from "framer-motion";
import { useTheme } from "next-themes";
import { useEffect, useState } from "react";

interface CodeSnippetProps {
  className?: string;
}

export default function CodeSnippet({ className }: CodeSnippetProps) {
  const { theme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Sample code snippets
  const reactCode = `import React, { useState } from 'react';

function Counter() {
  const [count, setCount] = useState(0);

  return (
    <div className="counter">
      <h2>Count: {count}</h2>
      <button onClick={() => setCount(count + 1)}>
        Increment
      </button>
    </div>
  );
}

export default Counter;`;

  const nextjsCode = `import { useState } from 'react';

export default function Home() {
  const [darkMode, setDarkMode] = useState(false);

  return (
    <div className={darkMode ? 'dark' : ''}>
    <title>My Portfolio</title>
      <main>
        <h1>Welcome to my portfolio!</h1>
        <button onClick={() => setDarkMode(!darkMode)}>
          Toggle theme
        </button>
      </main>
    </div>
  );
}`;

  const tailwindCode = `<div className="flex flex-col items-center p-6
  bg-gradient-to-br from-purple-500/20
  via-blue-500/20 to-pink-500/20 rounded-xl">
  <h2 className="text-2xl font-bold mb-4">
    My Portfolio
  </h2>
  <p className="text-gray-600 dark:text-gray-300
    text-center max-w-md">
    Frontend developer specializing in React,
    Next.js, and modern web technologies.
  </p>
  <button className="mt-4 px-6 py-2 bg-blue-500
    text-white rounded-full hover:bg-blue-600
    transition-colors">
    Contact Me
  </button>
</div>`;

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const lineVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10,
      },
    },
  };

  // Choose a random code snippet
  const codeSnippets = [reactCode, nextjsCode, tailwindCode];
  const randomIndex = Math.floor(Math.random() * codeSnippets.length);
  const selectedCode = codeSnippets[randomIndex];
  const codeLines = selectedCode.split("\n");

  if (!mounted) return null;

  const isDark = theme === "dark" || resolvedTheme === "dark";

  return (
    <div className={`code-snippet-container ${className}`}>
      <div
        className={`mb-2 flex items-center justify-between rounded-t-lg ${isDark ? "bg-gray-800/80" : "bg-gray-200/90"} px-4 py-2`}
      >
        <div className="flex space-x-2">
          <div className="h-3 w-3 rounded-full bg-red-500"></div>
          <div className="h-3 w-3 rounded-full bg-yellow-500"></div>
          <div className="h-3 w-3 rounded-full bg-green-500"></div>
        </div>
        <div
          className={`text-xs ${isDark ? "text-gray-400" : "text-gray-600"}`}
        >
          {randomIndex === 0
            ? "react-component.jsx"
            : randomIndex === 1
              ? "pages/index.js"
              : randomIndex === 2
                ? "types.ts"
                : "tailwind-component.jsx"}
        </div>
      </div>
      <div
        className={`overflow-hidden rounded-b-lg ${isDark ? "bg-gray-900/90" : "bg-gray-800/90"} p-4 font-mono text-sm`}
      >
        <motion.div
          initial="hidden"
          animate="visible"
          variants={containerVariants}
        >
          {codeLines.map((line, index) => (
            <motion.div
              key={index}
              variants={lineVariants}
              className="whitespace-pre"
              style={{
                color:
                  line.trim().startsWith("//") || line.trim().startsWith("/*")
                    ? "#6a9955" // Comments
                    : line.includes("import ") ||
                        line.includes("export ") ||
                        line.includes("function ") ||
                        line.includes("interface ") ||
                        line.includes("const ") ||
                        line.includes("return ")
                      ? "#569cd6" // Keywords
                      : line.includes("className=")
                        ? "#ce9178" // Strings
                        : line.includes("<") && line.includes(">")
                          ? "#9cdcfe" // JSX
                          : "#d4d4d4", // Default text
              }}
            >
              {line || " "}
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
  );
}
