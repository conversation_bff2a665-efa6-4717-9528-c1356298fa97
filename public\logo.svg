<?xml version="1.0" encoding="UTF-8"?>
<svg width="640" height="640" viewBox="0 0 640 640" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background elements -->
  <circle cx="320" cy="320" r="288" fill="url(#bgGradient)"/>
  <circle cx="320" cy="320" r="260" stroke="url(#glowGradient)" stroke-width="3" stroke-dasharray="12 8" opacity="0.5"/>
  <circle cx="320" cy="320" r="240" stroke="url(#pulseGradient)" stroke-width="1" opacity="0.3"/>
  
  <!-- Tech circuit pattern -->
  <g opacity="0.4">
    <path d="M120 320h80M440 320h80M320 120v80M320 440v80" 
          stroke="white" stroke-width="2" stroke-dasharray="8 4"/>
    <path d="M180 180L250 250M390 180L460 250M180 460L250 390M390 460L460 390" 
          stroke="white" stroke-width="2" stroke-dasharray="6 4"/>
  </g>
  
  <!-- Modern Angular M with double stroke effect -->
  <path d="M200 448V192L320 320L440 192V448" 
        stroke="url(#strokeGradient)" 
        stroke-width="48" 
        stroke-linecap="square" 
        stroke-linejoin="round"/>
  <path d="M200 448V192L320 320L440 192V448" 
        stroke="white" 
        stroke-width="40" 
        stroke-linecap="square" 
        stroke-linejoin="round"/>
  
  <!-- Central tech hub -->
  <circle cx="320" cy="320" r="32" fill="url(#centerGradient)"/>
  <circle cx="320" cy="320" r="48" stroke="white" stroke-width="3" stroke-dasharray="8 6" opacity="0.6">
    <animate attributeName="stroke-dashoffset" from="0" to="28" dur="3s" repeatCount="indefinite"/>
  </circle>
  
  <!-- Tech nodes -->
  <g opacity="0.9">
    <circle cx="200" cy="192" r="16" fill="url(#nodeGradient)"/>
    <circle cx="440" cy="192" r="16" fill="url(#nodeGradient)"/>
    <circle cx="200" cy="448" r="16" fill="url(#nodeGradient)"/>
    <circle cx="440" cy="448" r="16" fill="url(#nodeGradient)"/>
  </g>
  
  <!-- Connecting lines -->
  <path d="M320 272L320 368" stroke="white" stroke-width="6" stroke-linecap="round" opacity="0.7">
    <animate attributeName="stroke-dasharray" values="0,100;100,0" dur="2s" repeatCount="indefinite"/>
  </path>
  
  <!-- Enhanced gradients -->
  <defs>
    <linearGradient id="bgGradient" x1="32" y1="32" x2="608" y2="608" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="#2563EB"/>
      <stop offset="33%" stop-color="#7C3AED"/>
      <stop offset="66%" stop-color="#D946EF"/>
      <stop offset="100%" stop-color="#F43F5E"/>
    </linearGradient>
    
    <linearGradient id="glowGradient" x1="320" y1="60" x2="320" y2="580" gradientUnits="userSpaceOnUse">
      <stop offset="0%" stop-color="white" stop-opacity="0.9"/>
      <stop offset="50%" stop-color="white" stop-opacity="0.5"/>
      <stop offset="100%" stop-color="white" stop-opacity="0.9"/>
    </linearGradient>
    
    <radialGradient id="centerGradient" cx="0.5" cy="0.5" r="0.5" fx="0.5" fy="0.5">
      <stop offset="0%" stop-color="white"/>
      <stop offset="100%" stop-color="white" stop-opacity="0.6"/>
    </radialGradient>
    
    <radialGradient id="nodeGradient" cx="0.5" cy="0.5" r="0.5" fx="0.5" fy="0.5">
      <stop offset="0%" stop-color="white"/>
      <stop offset="100%" stop-color="white" stop-opacity="0.8"/>
    </radialGradient>
    
    <linearGradient id="strokeGradient" x1="200" y1="192" x2="440" y2="448">
      <stop offset="0%" stop-color="#60A5FA"/>
      <stop offset="100%" stop-color="#F472B6"/>
    </linearGradient>
    
    <linearGradient id="pulseGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="white" stop-opacity="0.4"/>
      <stop offset="50%" stop-color="white" stop-opacity="0.1"/>
      <stop offset="100%" stop-color="white" stop-opacity="0.4"/>
    </linearGradient>
  </defs>
</svg>