"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { toolCategories } from "@/data/tools";
import { cn } from "@/lib/utils";
import { AnimatePresence, motion } from "framer-motion";
import { Grid, List, Package, Search, Youtube } from "lucide-react";
import { useState } from "react";
import { PackageCard } from "./PackageCard";
import { ToolCard } from "./ToolCard";
import { YouTubeCard } from "./YouTubeCard";

type ViewMode = "grid" | "list";
type SortOption = "name" | "category" | "popularity";
type TabValue = "tools" | "packages" | "youtube";

export default function DevTools() {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedFramework, setSelectedFramework] = useState<string | null>(
    null,
  );
  const [viewMode, setViewMode] = useState<ViewMode>("grid");
  const [sortBy, setSortBy] = useState<SortOption>("name");
  const [activeTab, setActiveTab] = useState<TabValue>("tools");

  // Get unique frameworks for the filter
  const frameworks = Array.from(
    new Set(
      toolCategories
        .find((cat) => cat.id === "frontend-frameworks")
        ?.items.map((tool) => tool.name) || [],
    ),
  );

  // Flatten all tools into a single array for sorting and filtering
  const allTools = toolCategories.flatMap((category) =>
    category.items.map((tool) => ({
      ...tool,
      category: category.name,
      categoryId: category.id,
    })),
  );

  // Get all packages
  const allPackages = allTools.flatMap((tool) =>
    (tool.packages || []).map((pkg) => ({
      ...pkg,
      framework: tool.name,
      frameworkId: tool.name.toLowerCase(),
    })),
  );

  // Filter tools based on search, category, and framework
  const filteredTools = allTools.filter((tool) => {
    const matchesSearch =
      tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tool.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      tool.tags.some((tag) =>
        tag.toLowerCase().includes(searchQuery.toLowerCase()),
      ) ||
      tool.category.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesCategory =
      !selectedCategory || tool.categoryId === selectedCategory;
    const matchesFramework =
      !selectedFramework || tool.name === selectedFramework;

    return matchesSearch && matchesCategory && matchesFramework;
  });

  // Filter packages based on search and framework
  const filteredPackages = allPackages.filter((pkg) => {
    const matchesSearch =
      pkg.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      pkg.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      pkg.tags.some((tag) =>
        tag.toLowerCase().includes(searchQuery.toLowerCase()),
      );

    const matchesFramework =
      !selectedFramework || pkg.frameworkId === selectedFramework.toLowerCase();

    return matchesSearch && matchesFramework;
  });

  // Get YouTube channels
  const youtubeChannels =
    toolCategories.find((cat) => cat.id === "youtube-channels")?.items || [];

  // Filter YouTube channels based on search
  const filteredChannels = youtubeChannels.filter((channel) => {
    const matchesSearch =
      channel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      channel.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      channel.tags.some((tag) =>
        tag.toLowerCase().includes(searchQuery.toLowerCase()),
      );

    return matchesSearch;
  });

  // Sort tools based on selected option
  const sortedTools = [...filteredTools].sort((a, b) => {
    switch (sortBy) {
      case "name":
        return a.name.localeCompare(b.name);
      case "category":
        return a.category.localeCompare(b.category);
      case "popularity":
        const aPackages = a.packages?.length || 0;
        const bPackages = b.packages?.length || 0;
        return bPackages - aPackages;
      default:
        return 0;
    }
  });

  // Sort packages based on selected option
  const sortedPackages = [...filteredPackages].sort((a, b) => {
    switch (sortBy) {
      case "name":
        return a.name.localeCompare(b.name);
      case "category":
        return a.framework.localeCompare(b.framework);
      case "popularity":
        return (b.weeklyDownloads || "").localeCompare(a.weeklyDownloads || "");
      default:
        return 0;
    }
  });

  // Sort YouTube channels based on selected option
  const sortedChannels = [...filteredChannels].sort((a, b) => {
    switch (sortBy) {
      case "name":
        return a.name.localeCompare(b.name);
      case "popularity":
        return (b.subscribers || "").localeCompare(a.subscribers || "");
      default:
        return 0;
    }
  });

  // Group tools by category for list view
  const groupedTools = sortedTools.reduce(
    (acc, tool) => {
      if (!acc[tool.categoryId]) {
        acc[tool.categoryId] = {
          category: toolCategories.find((c) => c.id === tool.categoryId)!,
          tools: [],
        };
      }
      acc[tool.categoryId].tools.push(tool);
      return acc;
    },
    {} as Record<
      string,
      { category: (typeof toolCategories)[0]; tools: typeof sortedTools }
    >,
  );

  // Group packages by framework for list view
  const groupedPackages = sortedPackages.reduce(
    (acc, pkg) => {
      if (!acc[pkg.frameworkId]) {
        acc[pkg.frameworkId] = {
          framework: pkg.framework,
          packages: [],
        };
      }
      acc[pkg.frameworkId].packages.push(pkg);
      return acc;
    },
    {} as Record<
      string,
      { framework: string; packages: typeof sortedPackages }
    >,
  );

  return (
    <section className="container py-12">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="mb-12 space-y-6"
      >
        <div className="from-primary/10 via-primary/5 to-background relative overflow-hidden rounded-2xl bg-gradient-to-br p-8 md:p-12">
          <div className="bg-grid-white/10 absolute inset-0 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))]" />
          <div className="relative">
            <div className="text-center">
              <motion.h1
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
                className="from-primary to-primary/60 mb-4 bg-gradient-to-r bg-clip-text text-4xl font-bold tracking-tight text-transparent md:text-5xl"
              >
                Development Tools
              </motion.h1>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                className="text-muted-foreground mx-auto max-w-2xl text-lg"
              >
                A comprehensive collection of tools and technologies I use for
                efficient frontend development, from coding to deployment and
                everything in between.
              </motion.p>
            </div>
          </div>
        </div>

        <Tabs
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as TabValue)}
          className="relative"
        >
          <div className="flex flex-col gap-4">
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <div className="relative max-w-md flex-1">
                <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2" />
                <Input
                  type="search"
                  placeholder={
                    activeTab === "youtube"
                      ? "Search YouTube channels..."
                      : "Search tools and technologies..."
                  }
                  className="pl-9"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              <div className="flex items-center gap-2">
                <Select
                  value={sortBy}
                  onValueChange={(value) => setSortBy(value as SortOption)}
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Name</SelectItem>
                    {activeTab !== "youtube" && (
                      <SelectItem value="category">Category</SelectItem>
                    )}
                    <SelectItem value="popularity">Popularity</SelectItem>
                  </SelectContent>
                </Select>
                <div className="flex items-center gap-2">
                  <Button
                    variant={viewMode === "grid" ? "default" : "outline"}
                    size="icon"
                    onClick={() => setViewMode("grid")}
                    className="rounded-full"
                  >
                    <Grid className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === "list" ? "default" : "outline"}
                    size="icon"
                    onClick={() => setViewMode("list")}
                    className="rounded-full"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            <div className="flex flex-wrap items-center gap-4">
              <TabsList className="bg-secondary/50 p-1">
                <TabsTrigger
                  value="tools"
                  className="data-[state=active]:bg-background rounded-full"
                >
                  <Package className="mr-2 h-4 w-4" />
                  Tools
                </TabsTrigger>
                <TabsTrigger
                  value="packages"
                  className="data-[state=active]:bg-background rounded-full"
                >
                  <Package className="mr-2 h-4 w-4" />
                  Popular Packages
                </TabsTrigger>
                <TabsTrigger
                  value="youtube"
                  className="data-[state=active]:bg-background rounded-full"
                >
                  <Youtube className="mr-2 h-4 w-4" />
                  YouTube Channels
                </TabsTrigger>
              </TabsList>

              {activeTab !== "youtube" && (
                <div className="flex flex-wrap gap-2">
                  <button
                    onClick={() => setSelectedCategory(null)}
                    className={cn(
                      "rounded-full px-3 py-1.5 text-sm transition-colors",
                      !selectedCategory
                        ? "bg-primary text-primary-foreground"
                        : "bg-secondary text-secondary-foreground hover:bg-secondary/80",
                    )}
                  >
                    All Categories
                  </button>
                  {toolCategories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setSelectedCategory(category.id)}
                      className={cn(
                        "rounded-full px-3 py-1.5 text-sm transition-colors",
                        selectedCategory === category.id
                          ? "bg-primary text-primary-foreground"
                          : "bg-secondary text-secondary-foreground hover:bg-secondary/80",
                      )}
                    >
                      {category.name}
                    </button>
                  ))}
                </div>
              )}

              {activeTab !== "youtube" && (
                <Select
                  value={selectedFramework || "all"}
                  onValueChange={(value) =>
                    setSelectedFramework(value === "all" ? null : value)
                  }
                >
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by Framework" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Frameworks</SelectItem>
                    {frameworks.map((framework) => (
                      <SelectItem key={framework} value={framework}>
                        {framework}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>

          <TabsContent value="tools" className="mt-6">
            <AnimatePresence mode="wait">
              {viewMode === "grid" ? (
                <motion.div
                  key="grid"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3"
                >
                  {sortedTools.map((tool) => (
                    <ToolCard key={tool.name} tool={tool} />
                  ))}
                </motion.div>
              ) : (
                <motion.div
                  key="list"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="space-y-8"
                >
                  {Object.values(groupedTools).map(({ category, tools }) => (
                    <div key={category.id} className="space-y-4">
                      <div className="flex items-center gap-3">
                        <div className="bg-primary/10 text-primary rounded-lg p-2">
                          {category.icon}
                        </div>
                        <div className="flex-1">
                          <h2 className="from-foreground to-foreground/60 bg-gradient-to-r bg-clip-text text-2xl font-semibold tracking-tight text-transparent">
                            {category.name}
                          </h2>
                          <p className="text-muted-foreground mt-1">
                            {category.description}
                          </p>
                        </div>
                      </div>
                      <div className="grid gap-4">
                        {tools.map((tool) => (
                          <ToolCard key={tool.name} tool={tool} />
                        ))}
                      </div>
                    </div>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </TabsContent>

          <TabsContent value="packages" className="mt-6">
            <AnimatePresence mode="wait">
              {viewMode === "grid" ? (
                <motion.div
                  key="grid"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3"
                >
                  {sortedPackages.map((pkg) => (
                    <PackageCard key={pkg.name} pkg={pkg} />
                  ))}
                </motion.div>
              ) : (
                <motion.div
                  key="list"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="space-y-8"
                >
                  {Object.values(groupedPackages).map(
                    ({ framework, packages }) => (
                      <div key={framework} className="space-y-4">
                        <div className="flex items-center gap-3">
                          <div className="bg-primary/10 text-primary rounded-lg p-2">
                            <Package className="h-5 w-5" />
                          </div>
                          <div className="flex-1">
                            <h2 className="from-foreground to-foreground/60 bg-gradient-to-r bg-clip-text text-2xl font-semibold tracking-tight text-transparent">
                              {framework} Packages
                            </h2>
                          </div>
                        </div>
                        <div className="grid gap-4">
                          {packages.map((pkg) => (
                            <PackageCard key={pkg.name} pkg={pkg} />
                          ))}
                        </div>
                      </div>
                    ),
                  )}
                </motion.div>
              )}
            </AnimatePresence>
          </TabsContent>

          <TabsContent value="youtube" className="mt-6">
            <AnimatePresence mode="wait">
              {viewMode === "grid" ? (
                <motion.div
                  key="grid"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="grid gap-6 sm:grid-cols-2 lg:grid-cols-3"
                >
                  {sortedChannels.map((channel) => (
                    <YouTubeCard key={channel.name} channel={channel} />
                  ))}
                </motion.div>
              ) : (
                <motion.div
                  key="list"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="space-y-8"
                >
                  <div className="space-y-4">
                    <div className="flex items-center gap-3">
                      <div className="bg-primary/10 text-primary rounded-lg p-2">
                        <Youtube className="h-5 w-5" />
                      </div>
                      <div className="flex-1">
                        <h2 className="from-foreground to-foreground/60 bg-gradient-to-r bg-clip-text text-2xl font-semibold tracking-tight text-transparent">
                          YouTube Channels
                        </h2>
                      </div>
                    </div>
                    <div className="grid gap-4">
                      {sortedChannels.map((channel) => (
                        <YouTubeCard key={channel.name} channel={channel} />
                      ))}
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </TabsContent>
        </Tabs>

        {((activeTab === "tools" && filteredTools.length === 0) ||
          (activeTab === "packages" && filteredPackages.length === 0) ||
          (activeTab === "youtube" && filteredChannels.length === 0)) && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="py-12 text-center"
          >
            <p className="text-muted-foreground text-lg">
              No {activeTab} found matching your search criteria.
            </p>
          </motion.div>
        )}
      </motion.div>
    </section>
  );
}
