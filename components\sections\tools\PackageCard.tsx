import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Package } from "@/data/tools";
import { motion } from "framer-motion";
import { Download, ExternalLink, Package as PackageIcon } from "lucide-react";

interface PackageCardProps {
  pkg: Package;
}

export function PackageCard({ pkg }: PackageCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      whileHover={{ y: -5 }}
      transition={{ type: "spring", stiffness: 300 }}
    >
      <a
        href={pkg.npmUrl}
        target="_blank"
        rel="noopener noreferrer"
        className="block h-full"
      >
        <Card className="group hover:border-primary/50 relative h-full overflow-hidden border-2 transition-all duration-300 hover:shadow-lg">
          <div className="from-primary/5 absolute inset-0 bg-gradient-to-br via-transparent to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="bg-primary/10 text-primary rounded-xl p-2.5 transition-transform duration-300 group-hover:scale-110">
                  <PackageIcon className="h-5 w-5" />
                </div>
                <div>
                  <CardTitle className="text-xl font-semibold tracking-tight">
                    {pkg.name}
                  </CardTitle>
                  <CardDescription className="mt-1 line-clamp-2">
                    {pkg.description}
                  </CardDescription>
                </div>
              </div>
              <div className="flex gap-2">
                <a
                  href={pkg.npmUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-primary transition-colors"
                  onClick={(e) => e.stopPropagation()}
                >
                  <ExternalLink className="h-4 w-4" />
                </a>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex flex-wrap gap-2">
                {pkg.tags.map((tag) => (
                  <Badge
                    key={tag}
                    variant="secondary"
                    className="bg-secondary/50 text-secondary-foreground hover:bg-secondary/70 transition-colors duration-300"
                  >
                    {tag}
                  </Badge>
                ))}
              </div>
              <div className="text-muted-foreground flex items-center gap-4 text-sm">
                <div className="flex items-center gap-1">
                  <Download className="h-4 w-4" />
                  <span>{pkg.weeklyDownloads}</span>
                </div>
                {pkg.version && (
                  <span className="bg-secondary/50 rounded-full px-2 py-0.5">
                    v{pkg.version}
                  </span>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </a>
    </motion.div>
  );
}
