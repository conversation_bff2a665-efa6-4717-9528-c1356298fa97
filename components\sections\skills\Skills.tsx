"use client";

import GradientText from "@/components/effects/GradientText";
import { skills } from "@/data";
import { motion, useInView } from "framer-motion";
import { useMemo, useRef } from "react";
import SkillCategory from "./SkillCategory";

interface SkillsProps {
  limit?: number;
  showViewAllButton?: boolean;
}

export default function Skills({
  limit,
  showViewAllButton = false,
}: SkillsProps) {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  // Group skills by category
  const skillsByCategory = useMemo(() => {
    const categories = Array.from(
      new Set(skills.map((skill) => skill.category)),
    );

    const grouped = categories.map((category) => {
      let categorySkills = skills.filter(
        (skill) => skill.category === category,
      );

      // Apply limit if provided
      if (limit && limit > 0) {
        const skillsPerCategory = Math.ceil(limit / categories.length);
        categorySkills = categorySkills.slice(0, skillsPerCategory);
      }

      return {
        category,
        skills: categorySkills,
      };
    });

    return grouped;
  }, [limit]);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
      },
    },
  };

  return (
    <section
      id="skills"
      className="relative overflow-hidden py-24 sm:py-32"
      ref={sectionRef}
    >
      {/* Background elements - more dynamic and modern */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-amber-500/10" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,rgba(255,255,255,0.03)_0,transparent_70%)]" />
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] [mask-image:radial-gradient(white,transparent_85%)] bg-[size:40px_40px]" />

        {/* Animated gradient orbs */}
        <motion.div
          className="absolute top-1/4 left-1/4 h-96 w-96 rounded-full bg-blue-500/10 blur-3xl"
          animate={{
            x: [0, 30, 0],
            y: [0, -30, 0],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            repeatType: "reverse",
          }}
        />
        <motion.div
          className="absolute right-1/4 bottom-1/4 h-96 w-96 rounded-full bg-purple-500/10 blur-3xl"
          animate={{
            x: [0, -30, 0],
            y: [0, 30, 0],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            repeatType: "reverse",
          }}
        />
      </div>

      <div className="relative container">
        <motion.div
          className="mb-16 space-y-4 text-center"
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
        >
          <motion.span
            className="bg-primary/10 text-primary mb-2 inline-block rounded-full px-4 py-1.5 text-sm font-medium"
            variants={itemVariants}
          >
            My Expertise
          </motion.span>
          <motion.h2
            className="font-heading text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl"
            variants={itemVariants}
          >
            <GradientText>Skills & Technologies</GradientText>
          </motion.h2>
          <motion.p
            className="text-muted-foreground mx-auto max-w-[42rem] text-lg"
            variants={itemVariants}
          >
            My technical expertise and personal strengths that drive success in
            web development
          </motion.p>
        </motion.div>

        {/* Skills by category */}
        <motion.div
          className="mx-auto space-y-8"
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
        >
          {skillsByCategory.map((categoryGroup, index) => (
            <SkillCategory
              key={categoryGroup.category}
              category={categoryGroup.category}
              skills={categoryGroup.skills}
              index={index}
            />
          ))}
        </motion.div>

        {/* View All Skills button */}
        {showViewAllButton && (
          <motion.div
            className="mt-16 flex justify-center"
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
            transition={{ duration: 0.5, delay: 0.8 }}
          >
            <motion.a
              href="/skills"
              className="group border-primary/30 bg-primary/10 text-primary relative overflow-hidden rounded-full border-2 px-8 py-3 font-medium transition-all hover:shadow-lg"
              whileHover={{
                scale: 1.05,
                transition: { type: "spring", stiffness: 400 },
              }}
              whileTap={{ scale: 0.98 }}
            >
              <span className="relative z-10 flex items-center gap-2">
                View All Skills
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="transition-transform duration-300 group-hover:translate-x-1"
                >
                  <path d="M5 12h14"></path>
                  <path d="m12 5 7 7-7 7"></path>
                </svg>
              </span>
              <div className="bg-primary/5 absolute inset-0 -z-10 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
            </motion.a>
          </motion.div>
        )}
      </div>
    </section>
  );
}
