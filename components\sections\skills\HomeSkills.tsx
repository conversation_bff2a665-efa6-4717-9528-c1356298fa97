"use client";

import GradientText from "@/components/effects/GradientText";
import { skills } from "@/data";
import { motion, useInView } from "framer-motion";
import { useMemo, useRef, useState } from "react";

interface HomeSkillsProps {
  limit?: number;
  showViewAllButton?: boolean;
}

export default function HomeSkills({
  limit = 12,
  showViewAllButton = true,
}: HomeSkillsProps) {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });
  const [hoveredSkill, setHoveredSkill] = useState<string | null>(null);

  // Get top skills across all categories
  const topSkills = useMemo(() => {
    // Get a balanced selection from each category
    const categories = Array.from(
      new Set(skills.map((skill) => skill.category)),
    );

    const skillsPerCategory = Math.ceil(limit / categories.length);

    let selectedSkills: typeof skills = [];

    // Get top skills from each category
    categories.forEach((category) => {
      const categorySkills = skills
        .filter((s) => s.category === category)
        .sort((a, b) => {
          // Prioritize Vue.js and React
          if (a.name === "Vue.js") return -1;
          if (b.name === "Vue.js") return 1;
          if (a.name === "React") return -1;
          if (b.name === "React") return 1;
          // Then sort by proficiency
          return b.proficiency - a.proficiency;
        })
        .slice(0, skillsPerCategory);

      selectedSkills = [...selectedSkills, ...categorySkills];
    });

    // If we have more than the limit, trim the excess
    if (selectedSkills.length > limit) {
      selectedSkills = selectedSkills.slice(0, limit);
    }

    return selectedSkills;
  }, [limit]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  // Get color for skill based on category
  const getSkillColor = (category: string) => {
    switch (category) {
      case "Frontend":
        return "from-blue-600/80 to-cyan-400/80";
      case "Backend":
        return "from-green-600/80 to-emerald-400/80";
      case "Tools":
        return "from-purple-600/80 to-violet-400/80";
      case "Soft Skills":
        return "from-amber-600/80 to-orange-400/80";
      default:
        return "from-gray-600/80 to-slate-400/80";
    }
  };

  // Get text color for skill based on category
  const getTextColor = (category: string) => {
    switch (category) {
      case "Frontend":
        return "text-blue-500";
      case "Backend":
        return "text-green-500";
      case "Tools":
        return "text-purple-500";
      case "Soft Skills":
        return "text-amber-500";
      default:
        return "text-gray-500";
    }
  };

  return (
    <section
      id="skills"
      className="relative overflow-hidden py-24 sm:py-32"
      ref={sectionRef}
    >
      {/* Background elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-amber-500/5" />

        {/* Animated gradient orbs */}
        <motion.div
          className="absolute top-1/4 left-1/4 h-64 w-64 rounded-full bg-blue-500/10 blur-3xl"
          animate={{
            x: [0, 20, 0],
            y: [0, -20, 0],
            scale: [1, 1.1, 1],
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            repeatType: "reverse",
          }}
        />
        <motion.div
          className="absolute right-1/4 bottom-1/4 h-64 w-64 rounded-full bg-purple-500/10 blur-3xl"
          animate={{
            x: [0, -20, 0],
            y: [0, 20, 0],
            scale: [1, 1.2, 1],
          }}
          transition={{
            duration: 18,
            repeat: Infinity,
            repeatType: "reverse",
          }}
        />
      </div>

      <div className="relative container">
        <motion.div
          className="mb-16 space-y-4 text-center"
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
        >
          <motion.span
            className="bg-primary/10 text-primary mb-2 inline-block rounded-full px-4 py-1.5 text-sm font-medium"
            variants={itemVariants}
          >
            My Expertise
          </motion.span>
          <motion.h2
            className="font-heading text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl"
            variants={itemVariants}
          >
            <GradientText>Skills & Technologies</GradientText>
          </motion.h2>
          <motion.p
            className="text-muted-foreground mx-auto max-w-[42rem] text-lg"
            variants={itemVariants}
          >
            My technical expertise and personal strengths that drive success in
            web development
          </motion.p>
        </motion.div>

        {/* Skills hexagon grid */}
        <motion.div
          className="mx-auto max-w-4xl"
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
        >
          <div className="flex flex-wrap justify-center gap-4 md:gap-6">
            {topSkills.map((skill) => (
              <motion.div
                key={skill.name}
                className="group relative"
                variants={itemVariants}
                onHoverStart={() => setHoveredSkill(skill.name)}
                onHoverEnd={() => setHoveredSkill(null)}
                whileHover={{ scale: 1.1, zIndex: 10 }}
                transition={{ type: "spring", stiffness: 300, damping: 20 }}
              >
                {/* Hexagon shape */}
                <div className="relative h-24 w-24 md:h-28 md:w-28">
                  <div
                    className={`absolute inset-0 bg-gradient-to-br ${getSkillColor(skill.category)} transform rounded-2xl shadow-lg transition-all duration-300 group-hover:shadow-xl`}
                    style={{
                      clipPath:
                        "polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%)",
                    }}
                  />

                  {/* Icon */}
                  <div className="absolute inset-0 flex items-center justify-center text-white">
                    <div className="text-3xl md:text-4xl">{skill.icon}</div>
                  </div>
                </div>

                {/* Tooltip */}
                <div
                  className={`bg-background/90 absolute top-full left-1/2 z-20 mt-2 min-w-[120px] -translate-x-1/2 rounded-lg p-2 text-center text-sm font-medium shadow-lg backdrop-blur-sm transition-all duration-300 ${
                    hoveredSkill === skill.name
                      ? "scale-100 opacity-100"
                      : "pointer-events-none scale-95 opacity-0"
                  }`}
                >
                  <div className={`font-bold ${getTextColor(skill.category)}`}>
                    {skill.name}
                  </div>
                  <div className="text-xs opacity-70">{skill.category}</div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* View All Skills button */}
          {showViewAllButton && (
            <motion.div
              className="mt-16 flex justify-center"
              variants={itemVariants}
            >
              <motion.a
                href="/skills"
                className="group border-primary/30 bg-primary/10 text-primary relative overflow-hidden rounded-full border-2 px-8 py-3 font-medium transition-all hover:shadow-lg"
                whileHover={{
                  scale: 1.05,
                  transition: { type: "spring", stiffness: 400 },
                }}
                whileTap={{ scale: 0.98 }}
              >
                <span className="relative z-10 flex items-center gap-2">
                  View All Skills
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="transition-transform duration-300 group-hover:translate-x-1"
                  >
                    <path d="M5 12h14"></path>
                    <path d="m12 5 7 7-7 7"></path>
                  </svg>
                </span>
                <div className="bg-primary/5 absolute inset-0 -z-10 opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
              </motion.a>
            </motion.div>
          )}
        </motion.div>
      </div>
    </section>
  );
}
