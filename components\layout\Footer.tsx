"use client";

import { But<PERSON> } from "@/components/ui/button";
import { URLS } from "@/utils/urls";
import { motion } from "framer-motion";
import { ArrowUpRight, Heart, Mail, MapPin } from "lucide-react";
import Link from "next/link";
import { FaGithub, FaLinkedin } from "react-icons/fa";

export default function Footer() {
  const currentYear = new Date().getFullYear();

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 100,
        damping: 10,
      },
    },
  };

  const quickLinks = [
    { href: "/", label: "Home" },
    { href: "/about", label: "About" },
    { href: "/skills", label: "Skills" },
    { href: "/projects", label: "Projects" },
    { href: "/experience", label: "Experience" },
    { href: "/contact", label: "Contact" },
  ];

  const socialLinks = [
    {
      href: URLS.github,
      icon: <FaGithub className="h-5 w-5" />,
      label: "GitHub",
    },
    {
      href: URLS.linkedin,
      icon: <FaLinkedin className="h-5 w-5" />,
      label: "LinkedIn",
    },
    { href: URLS.email, icon: <Mail className="h-5 w-5" />, label: "Email" },
  ];

  return (
    <footer className="from-background to-background/95 relative overflow-hidden border-t bg-gradient-to-b">
      {/* Background decorative elements */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute -top-20 -left-20 h-64 w-64 rounded-full bg-purple-500/5 blur-3xl" />
        <div className="absolute -right-20 -bottom-20 h-64 w-64 rounded-full bg-blue-500/5 blur-3xl" />
      </div>

      <div className="container py-16">
        <motion.div
          className="mx-auto max-w-6xl"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          <div className="mb-16 grid grid-cols-1 gap-12 md:grid-cols-2 lg:grid-cols-4">
            {/* Brand Section */}
            <motion.div className="space-y-6" variants={itemVariants}>
              <div>
                <div className="flex items-center gap-2">
                  <img src="/logo.svg" alt="Logo" className="h-8 w-8" />
                  <h3 className="font-heading mb-1 text-2xl font-bold tracking-tight">
                    <span className="inline-block bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500 bg-clip-text text-transparent">
                      Mohamed Zaki
                    </span>
                  </h3>
                </div>
                <p className="text-muted-foreground">Frontend Developer</p>
              </div>

              <p className="text-muted-foreground max-w-md">
                Building beautiful web experiences with modern technologies and
                a focus on user experience.
              </p>

              <div className="flex gap-3">
                {socialLinks.map((social) => (
                  <motion.div
                    key={social.href}
                    whileHover={{ y: -5 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <Link
                      href={social.href}
                      target="_blank"
                      aria-label={social.label}
                    >
                      <Button
                        variant="outline"
                        size="icon"
                        className="border-border hover:border-primary/50 hover:bg-primary/5 hover:text-primary h-10 w-10 rounded-full transition-all"
                      >
                        {social.icon}
                      </Button>
                    </Link>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Quick Links */}
            <motion.div className="space-y-6" variants={itemVariants}>
              <h3 className="font-heading text-lg font-semibold">
                Quick Links
              </h3>
              <nav className="flex flex-col space-y-3">
                {quickLinks.map((link) => (
                  <motion.div
                    key={link.href}
                    whileHover={{ x: 5 }}
                    transition={{ type: "spring", stiffness: 300 }}
                  >
                    <Link
                      href={link.href}
                      className="group text-muted-foreground hover:text-primary flex items-center transition-colors"
                    >
                      <span className="bg-muted-foreground group-hover:bg-primary mr-2 h-1 w-1 rounded-full transition-all group-hover:w-2" />
                      {link.label}
                    </Link>
                  </motion.div>
                ))}
              </nav>
            </motion.div>

            {/* Contact Info */}
            <motion.div className="space-y-6" variants={itemVariants}>
              <h3 className="font-heading text-lg font-semibold">Contact</h3>
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <Mail className="text-muted-foreground mt-0.5 h-5 w-5" />
                  <div>
                    <p className="font-medium">Email</p>
                    <Link
                      href={URLS.email}
                      className="text-muted-foreground hover:text-primary transition-colors"
                    >
                      <EMAIL>
                    </Link>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <MapPin className="text-muted-foreground mt-0.5 h-5 w-5" />
                  <div>
                    <p className="font-medium">Location</p>
                    <p className="text-muted-foreground">Mansoura, Egypt</p>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Newsletter */}
            <motion.div className="space-y-6" variants={itemVariants}>
              <h3 className="font-heading text-lg font-semibold">
                Let&apos;s Connect
              </h3>
              <p className="text-muted-foreground">
                Interested in working together? Let&apos;s discuss your project.
              </p>
              <Link href="/contact">
                <Button className="group flex w-full items-center justify-between gap-2 rounded-full">
                  <span>Get in Touch</span>
                  <ArrowUpRight className="h-4 w-4 transition-transform group-hover:rotate-45" />
                </Button>
              </Link>
            </motion.div>
          </div>

          {/* Copyright */}
          <motion.div
            className="flex flex-col items-center justify-between gap-4 border-t pt-8 text-center sm:flex-row sm:text-left"
            variants={itemVariants}
          >
            <p className="text-muted-foreground text-sm">
              © {currentYear} Mohamed Zaki. All rights reserved.
            </p>

            <p className="text-muted-foreground flex items-center gap-1 text-sm">
              Built with
              <motion.span
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1.5, repeat: Infinity }}
              >
                <Heart className="h-4 w-4 text-red-500" />
              </motion.span>
              using Next.js & Tailwind CSS
            </p>
          </motion.div>
        </motion.div>
      </div>
    </footer>
  );
}
