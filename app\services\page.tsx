"use client";

import GradientText from "@/components/effects/GradientText";
import { Button } from "@/components/ui/button";
import { URLS } from "@/utils/urls";
import { motion } from "framer-motion";
import {
  ArrowRight,
  CheckCircle2,
  Code2,
  Layers,
  LayoutGrid,
  MessageSquare,
  Smartphone,
  Sparkles,
  Star,
  ThumbsUp,
  Workflow,
  Zap,
} from "lucide-react";
import Link from "next/link";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  SiNextdotjs,
  SiReact,
  SiTailwindcss,
  SiTypescript,
} from "react-icons/si";

export default function ServicesPage() {
  return (
    <main className="pt-16">
      <Services />
    </main>
  );
}

function Services() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: "spring",
        stiffness: 260,
        damping: 20,
      },
    },
  };

  const services = [
    {
      icon: <SiReact className="h-10 w-10" />,
      title: "React Development",
      description:
        "Custom React applications with modern features and state management",
      color: "from-blue-500/20 to-cyan-500/20",
      textColor: "text-blue-500",
      features: [
        "Custom Component Development",
        "State Management (Redux, Context)",
        "Performance Optimization",
        "React Hooks Implementation",
      ],
    },
    {
      icon: <SiNextdotjs className="h-10 w-10" />,
      title: "Next.js Solutions",
      description: "Full-stack Next.js applications with modern features",
      color: "from-gray-500/20 to-slate-500/20",
      textColor: "text-gray-500",
      features: [
        "Server-side Rendering (SSR)",
        "Static Site Generation (SSG)",
        "API Routes Integration",
        "Dynamic Routing",
      ],
    },
    {
      icon: <SiTailwindcss className="h-10 w-10" />,
      title: "Modern UI Development",
      description: "Clean and modern user interfaces with latest design trends",
      color: "from-cyan-500/20 to-teal-500/20",
      textColor: "text-cyan-500",
      features: [
        "Tailwind CSS Integration",
        "Custom Animations",
        "Accessible Components",
        "Dark Mode Support",
      ],
    },
    {
      icon: <SiTypescript className="h-10 w-10" />,
      title: "TypeScript Development",
      description: "Type-safe applications with enhanced maintainability",
      color: "from-blue-500/20 to-indigo-500/20",
      textColor: "text-blue-500",
      features: [
        "Type Safety Implementation",
        "Code Architecture",
        "Migration to TypeScript",
        "Best Practices",
      ],
    },
    {
      icon: <SiFramer className="h-10 w-10" />,
      title: "Animation & Interaction",
      description:
        "Engaging animations and interactive elements for better user experience",
      color: "from-purple-500/20 to-violet-500/20",
      textColor: "text-purple-500",
      features: [
        "Framer Motion Animations",
        "Interactive UI Elements",
        "Micro-interactions",
        "Smooth Page Transitions",
      ],
    },
    {
      icon: <Smartphone className="h-10 w-10" />,
      title: "Responsive Design",
      description:
        "Mobile-first web applications that work seamlessly across all devices",
      color: "from-green-500/20 to-emerald-500/20",
      textColor: "text-green-500",
      features: [
        "Mobile-First Development",
        "Cross-Browser Compatibility",
        "Responsive Layouts",
        "Touch-Friendly Interfaces",
      ],
    },
  ];

  const processSteps = [
    {
      icon: <MessageSquare className="h-6 w-6" />,
      title: "Discovery & Planning",
      description:
        "We start by understanding your requirements, goals, and vision for the project.",
    },
    {
      icon: <LayoutGrid className="h-6 w-6" />,
      title: "Design & Prototyping",
      description:
        "Creating wireframes and interactive prototypes to visualize the solution.",
    },
    {
      icon: <Code2 className="h-6 w-6" />,
      title: "Development",
      description:
        "Building the application with clean, maintainable code and modern technologies.",
    },
    {
      icon: <Sparkles className="h-6 w-6" />,
      title: "Testing & Refinement",
      description:
        "Thorough testing and refinement to ensure quality and performance.",
    },
    {
      icon: <Layers className="h-6 w-6" />,
      title: "Deployment",
      description:
        "Launching your application with proper setup and configuration.",
    },
    {
      icon: <Workflow className="h-6 w-6" />,
      title: "Support & Maintenance",
      description:
        "Ongoing support and updates to keep your application running smoothly.",
    },
  ];

  const testimonials = [
    {
      quote:
        "Mohamed delivered a fantastic React application that exceeded our expectations. His attention to detail and technical expertise made the project a success.",
      author: "Sarah Johnson",
      role: "Product Manager",
    },
    {
      quote:
        "Working with Mohamed was a great experience. He understood our requirements perfectly and delivered a high-quality Next.js website on time.",
      author: "Michael Chen",
      role: "Startup Founder",
    },
    {
      quote:
        "The UI/UX improvements Mohamed made to our application significantly enhanced user engagement and satisfaction.",
      author: "Alex Rodriguez",
      role: "Marketing Director",
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative overflow-hidden py-24 sm:py-32">
        {/* Background elements */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-cyan-500/5" />
          <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:40px_40px] [mask-image:radial-gradient(white,transparent_85%)]" />
        </div>

        <div className="relative container">
          <motion.div
            className="grid items-center gap-12 lg:grid-cols-2"
            initial="hidden"
            animate="visible"
            variants={containerVariants}
          >
            <div className="space-y-8">
              <motion.span
                className="bg-primary/10 text-primary inline-block rounded-full px-4 py-1.5 text-sm font-medium"
                variants={itemVariants}
              >
                Professional Services
              </motion.span>
              <motion.h1
                className="font-heading text-4xl font-bold tracking-tight sm:text-5xl md:text-6xl"
                variants={itemVariants}
              >
                <GradientText>Web Development Services</GradientText>
              </motion.h1>
              <motion.p
                className="text-muted-foreground max-w-2xl text-lg"
                variants={itemVariants}
              >
                Professional web development services focused on creating
                modern, performant, and scalable applications using React and
                Next.js
              </motion.p>
              <motion.div
                className="flex flex-wrap gap-4"
                variants={itemVariants}
              >
                <Link href={URLS.whatsapp} target="_blank">
                  <Button size="lg" className="gap-2 rounded-full">
                    Start Your Project
                    <Zap className="h-4 w-4" />
                  </Button>
                </Link>
                <Link href="#services">
                  <Button
                    size="lg"
                    variant="outline"
                    className="gap-2 rounded-full"
                  >
                    Explore Services
                    <ArrowRight className="h-4 w-4" />
                  </Button>
                </Link>
              </motion.div>
            </div>

            <motion.div
              className="relative mx-auto max-w-lg"
              variants={itemVariants}
            >
              <div className="relative aspect-square overflow-hidden rounded-2xl bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-cyan-500/10 p-1 backdrop-blur-sm">
                <div className="bg-background/80 h-full w-full rounded-xl p-6">
                  <div className="relative h-full w-full overflow-hidden rounded-lg bg-gradient-to-br from-blue-500/20 via-purple-500/20 to-cyan-500/20">
                    <div className="flex h-full w-full flex-col items-center justify-center p-6 text-center">
                      <div className="mb-4 text-5xl">💻</div>
                      <h3 className="text-xl font-bold">Web Development</h3>
                      <p className="text-muted-foreground mt-2 text-sm">
                        Modern & Responsive Solutions
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Floating elements */}
              <motion.div
                className="absolute -top-6 -right-6 rounded-full bg-blue-500/10 p-3 backdrop-blur-sm"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.5 }}
              >
                <SiReact className="h-8 w-8 text-blue-500" />
              </motion.div>
              <motion.div
                className="absolute -bottom-6 -left-6 rounded-full bg-purple-500/10 p-3 backdrop-blur-sm"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7, duration: 0.5 }}
              >
                <SiNextdotjs className="h-8 w-8 text-gray-500" />
              </motion.div>
              <motion.div
                className="absolute top-1/2 -right-4 rounded-full bg-cyan-500/10 p-3 backdrop-blur-sm"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.9, duration: 0.5 }}
              >
                <SiTailwindcss className="h-8 w-8 text-cyan-500" />
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Services Section */}
      <section id="services" className="py-24 sm:py-32">
        <div className="container">
          <div className="mb-16 text-center">
            <span className="bg-primary/10 text-primary mb-2 inline-block rounded-full px-4 py-1.5 text-sm font-medium">
              What I Offer
            </span>
            <h2 className="font-heading mb-4 text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl">
              <GradientText>Specialized Services</GradientText>
            </h2>
            <p className="text-muted-foreground mx-auto max-w-2xl text-lg">
              Comprehensive web development services tailored to your specific
              needs
            </p>
          </div>

          <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {services.map((service, index) => (
              <motion.div
                key={index}
                className={`group relative overflow-hidden rounded-xl border border-transparent bg-gradient-to-br ${service.color} p-1 backdrop-blur-sm transition-all duration-300 hover:shadow-xl`}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, margin: "-50px" }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ y: -5, transition: { duration: 0.2 } }}
              >
                <div className="bg-background/80 h-full rounded-lg p-6">
                  <div
                    className={`mb-4 flex items-center gap-4 ${service.textColor}`}
                  >
                    <div className="flex h-16 w-16 items-center justify-center rounded-lg bg-white/5 backdrop-blur-sm">
                      {service.icon}
                    </div>
                    <h3 className="text-xl font-bold">{service.title}</h3>
                  </div>
                  <p className="text-muted-foreground mb-6">
                    {service.description}
                  </p>
                  <ul className="space-y-2">
                    {service.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start gap-2">
                        <CheckCircle2
                          className={`mt-0.5 h-4 w-4 ${service.textColor}`}
                        />
                        <span className="text-sm">{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="relative overflow-hidden py-24 sm:py-32">
        {/* Background elements */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-cyan-500/5" />
          <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:40px_40px] [mask-image:radial-gradient(white,transparent_85%)]" />
        </div>

        <div className="container">
          <div className="mb-16 text-center">
            <span className="bg-primary/10 text-primary mb-2 inline-block rounded-full px-4 py-1.5 text-sm font-medium">
              How I Work
            </span>
            <h2 className="font-heading mb-4 text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl">
              <GradientText>Development Process</GradientText>
            </h2>
            <p className="text-muted-foreground mx-auto max-w-2xl text-lg">
              A structured approach to ensure successful project delivery and
              client satisfaction
            </p>
          </div>

          <div className="mx-auto max-w-5xl">
            {/* Process steps cards */}
            <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
              {processSteps.map((step, index) => (
                <motion.div
                  key={index}
                  className="group relative overflow-hidden rounded-xl border border-transparent bg-white/5 p-6 backdrop-blur-sm transition-all duration-300 hover:shadow-xl"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true, margin: "-50px" }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{
                    y: -5,
                    transition: { duration: 0.2 },
                    boxShadow:
                      "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
                  }}
                >
                  {/* Step number */}
                  <div className="text-primary absolute -top-4 -right-4 flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-blue-500/10 to-purple-500/10 text-xl font-bold opacity-30">
                    {index + 1}
                  </div>

                  <div className="relative z-10">
                    {/* Icon with gradient background */}
                    <div className="text-primary mb-6 inline-flex h-14 w-14 items-center justify-center rounded-lg bg-gradient-to-br from-blue-500/20 to-purple-500/20 transition-transform duration-300 group-hover:scale-110">
                      {step.icon}
                    </div>

                    {/* Title and description */}
                    <h3 className="mb-3 text-xl font-bold">{step.title}</h3>
                    <p className="text-muted-foreground mb-4">
                      {step.description}
                    </p>

                    {/* Additional details based on step */}
                    {index === 0 && (
                      <ul className="text-muted-foreground mt-3 space-y-1 text-sm">
                        <li className="flex items-center gap-2">
                          <span className="h-1.5 w-1.5 rounded-full bg-blue-500/70"></span>
                          <span>Requirement gathering</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <span className="h-1.5 w-1.5 rounded-full bg-blue-500/70"></span>
                          <span>Project scope definition</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <span className="h-1.5 w-1.5 rounded-full bg-blue-500/70"></span>
                          <span>Timeline planning</span>
                        </li>
                      </ul>
                    )}

                    {index === 1 && (
                      <ul className="text-muted-foreground mt-3 space-y-1 text-sm">
                        <li className="flex items-center gap-2">
                          <span className="h-1.5 w-1.5 rounded-full bg-purple-500/70"></span>
                          <span>UI/UX wireframing</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <span className="h-1.5 w-1.5 rounded-full bg-purple-500/70"></span>
                          <span>Interactive prototypes</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <span className="h-1.5 w-1.5 rounded-full bg-purple-500/70"></span>
                          <span>Design approval</span>
                        </li>
                      </ul>
                    )}

                    {index === 2 && (
                      <ul className="text-muted-foreground mt-3 space-y-1 text-sm">
                        <li className="flex items-center gap-2">
                          <span className="h-1.5 w-1.5 rounded-full bg-cyan-500/70"></span>
                          <span>Frontend implementation</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <span className="h-1.5 w-1.5 rounded-full bg-cyan-500/70"></span>
                          <span>Backend integration</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <span className="h-1.5 w-1.5 rounded-full bg-cyan-500/70"></span>
                          <span>Code reviews</span>
                        </li>
                      </ul>
                    )}

                    {index === 3 && (
                      <ul className="text-muted-foreground mt-3 space-y-1 text-sm">
                        <li className="flex items-center gap-2">
                          <span className="h-1.5 w-1.5 rounded-full bg-amber-500/70"></span>
                          <span>Functional testing</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <span className="h-1.5 w-1.5 rounded-full bg-amber-500/70"></span>
                          <span>Performance optimization</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <span className="h-1.5 w-1.5 rounded-full bg-amber-500/70"></span>
                          <span>Client feedback integration</span>
                        </li>
                      </ul>
                    )}

                    {index === 4 && (
                      <ul className="text-muted-foreground mt-3 space-y-1 text-sm">
                        <li className="flex items-center gap-2">
                          <span className="h-1.5 w-1.5 rounded-full bg-green-500/70"></span>
                          <span>Server configuration</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <span className="h-1.5 w-1.5 rounded-full bg-green-500/70"></span>
                          <span>Domain setup</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <span className="h-1.5 w-1.5 rounded-full bg-green-500/70"></span>
                          <span>Launch preparation</span>
                        </li>
                      </ul>
                    )}

                    {index === 5 && (
                      <ul className="text-muted-foreground mt-3 space-y-1 text-sm">
                        <li className="flex items-center gap-2">
                          <span className="h-1.5 w-1.5 rounded-full bg-indigo-500/70"></span>
                          <span>Regular updates</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <span className="h-1.5 w-1.5 rounded-full bg-indigo-500/70"></span>
                          <span>Bug fixes</span>
                        </li>
                        <li className="flex items-center gap-2">
                          <span className="h-1.5 w-1.5 rounded-full bg-indigo-500/70"></span>
                          <span>Feature enhancements</span>
                        </li>
                      </ul>
                    )}
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Process flow diagram - visible on larger screens */}
            <motion.div
              className="mt-16 hidden lg:block"
              initial={{ opacity: 0 }}
              whileInView={{ opacity: 1 }}
              viewport={{ once: true }}
              transition={{ duration: 0.8 }}
            >
              <div className="relative mx-auto h-24 max-w-3xl">
                {/* Connecting line */}
                <div className="absolute top-1/2 left-0 h-1 w-full -translate-y-1/2 bg-gradient-to-r from-blue-500/30 via-purple-500/30 to-cyan-500/30"></div>

                {/* Process points */}
                {[0, 1, 2, 3, 4, 5].map((index) => (
                  <motion.div
                    key={index}
                    className="absolute top-1/2 flex -translate-y-1/2 flex-col items-center"
                    style={{ left: `${index * 20}%` }}
                    initial={{ scale: 0 }}
                    whileInView={{ scale: 1 }}
                    viewport={{ once: true }}
                    transition={{ delay: index * 0.1, duration: 0.5 }}
                  >
                    <div className="text-primary flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20">
                      {index + 1}
                    </div>
                    <div className="mt-2 text-xs font-medium">
                      {processSteps[index].title.split(" ")[0]}
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="relative overflow-hidden py-24 sm:py-32">
        {/* Background elements */}
        <div className="absolute inset-0 -z-10">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-purple-500/5 to-amber-500/5" />
          <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:40px_40px] [mask-image:radial-gradient(white,transparent_85%)]" />
        </div>

        <div className="container">
          <div className="mb-16 text-center">
            <span className="bg-primary/10 text-primary mb-2 inline-block rounded-full px-4 py-1.5 text-sm font-medium">
              Client Feedback
            </span>
            <h2 className="font-heading mb-4 text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl">
              <GradientText>What Clients Say</GradientText>
            </h2>
            <p className="text-muted-foreground mx-auto max-w-2xl text-lg">
              Hear from clients about their experience working with me
            </p>
          </div>

          <div className="mx-auto max-w-5xl">
            <div className="grid gap-8 md:grid-cols-3">
              <motion.div
                className="group relative overflow-hidden rounded-xl border border-transparent bg-gradient-to-br from-blue-500/20 to-cyan-500/20 p-1 backdrop-blur-sm transition-all duration-300 hover:shadow-xl"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, margin: "-50px" }}
                transition={{ duration: 0.5, delay: 0.1 }}
                whileHover={{
                  y: -5,
                  transition: { duration: 0.2 },
                }}
              >
                <div className="bg-background/80 h-full rounded-lg p-6">
                  {/* Quote mark */}
                  <div className="mb-4 text-4xl text-blue-500">&ldquo;</div>

                  {/* Testimonial content */}
                  <p className="text-muted-foreground mb-6">
                    {testimonials[0].quote}
                  </p>

                  {/* Rating stars */}
                  <div className="mb-4 flex">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-4 w-4 fill-current text-blue-500"
                      />
                    ))}
                  </div>

                  {/* Author info */}
                  <div className="flex items-center gap-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-blue-500/20 to-cyan-500/20 text-blue-500">
                      <ThumbsUp className="h-5 w-5" />
                    </div>
                    <div>
                      <p className="font-semibold">{testimonials[0].author}</p>
                      <p className="text-muted-foreground text-sm">
                        {testimonials[0].role} at TechVision
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>

              <motion.div
                className="group relative overflow-hidden rounded-xl border border-transparent bg-gradient-to-br from-amber-500/20 to-orange-500/20 p-1 backdrop-blur-sm transition-all duration-300 hover:shadow-xl"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, margin: "-50px" }}
                transition={{ duration: 0.5, delay: 0.2 }}
                whileHover={{
                  y: -5,
                  transition: { duration: 0.2 },
                }}
              >
                <div className="bg-background/80 h-full rounded-lg p-6">
                  {/* Quote mark */}
                  <div className="mb-4 text-4xl text-amber-500">&ldquo;</div>

                  {/* Testimonial content */}
                  <p className="text-muted-foreground mb-6">
                    {testimonials[1].quote}
                  </p>

                  {/* Rating stars */}
                  <div className="mb-4 flex">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-4 w-4 fill-current text-amber-500"
                      />
                    ))}
                  </div>

                  {/* Author info */}
                  <div className="flex items-center gap-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-amber-500/20 to-orange-500/20 text-amber-500">
                      <Star className="h-5 w-5" />
                    </div>
                    <div>
                      <p className="font-semibold">{testimonials[1].author}</p>
                      <p className="text-muted-foreground text-sm">
                        {testimonials[1].role} at InnovateLabs
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>

              <motion.div
                className="group relative overflow-hidden rounded-xl border border-transparent bg-gradient-to-br from-purple-500/20 to-violet-500/20 p-1 backdrop-blur-sm transition-all duration-300 hover:shadow-xl"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, margin: "-50px" }}
                transition={{ duration: 0.5, delay: 0.3 }}
                whileHover={{
                  y: -5,
                  transition: { duration: 0.2 },
                }}
              >
                <div className="bg-background/80 h-full rounded-lg p-6">
                  {/* Quote mark */}
                  <div className="mb-4 text-4xl text-purple-500">&ldquo;</div>

                  {/* Testimonial content */}
                  <p className="text-muted-foreground mb-6">
                    {testimonials[2].quote}
                  </p>

                  {/* Rating stars */}
                  <div className="mb-4 flex">
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className="h-4 w-4 fill-current text-purple-500"
                      />
                    ))}
                  </div>

                  {/* Author info */}
                  <div className="flex items-center gap-3">
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br from-purple-500/20 to-violet-500/20 text-purple-500">
                      <MessageSquare className="h-5 w-5" />
                    </div>
                    <div>
                      <p className="font-semibold">{testimonials[2].author}</p>
                      <p className="text-muted-foreground text-sm">
                        {testimonials[2].role} at GrowthTech
                      </p>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>

            {/* Decorative elements */}
            <div className="pointer-events-none absolute -bottom-16 -left-16 h-64 w-64 rounded-full bg-blue-500/5 blur-3xl" />
            <div className="pointer-events-none absolute -top-16 -right-16 h-64 w-64 rounded-full bg-purple-500/5 blur-3xl" />
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-cyan-500/10 py-24 sm:py-32">
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:40px_40px] [mask-image:radial-gradient(white,transparent_85%)]"></div>

        <div className="relative container">
          <div className="mx-auto max-w-3xl text-center">
            <motion.h2
              className="font-heading mb-6 text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
            >
              Ready to Start Your Project?
            </motion.h2>
            <motion.p
              className="text-muted-foreground mb-8 text-lg"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
            >
              Let&apos;s work together to bring your vision to life with modern
              web technologies
            </motion.p>
            <motion.div
              className="flex flex-wrap justify-center gap-4"
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
            >
              <Link href={URLS.whatsapp} target="_blank">
                <Button size="lg" className="gap-2 rounded-full">
                  Contact on WhatsApp
                  <Zap className="h-4 w-4" />
                </Button>
              </Link>
              <Link href="/contact">
                <Button
                  size="lg"
                  variant="outline"
                  className="gap-2 rounded-full"
                >
                  Other Contact Methods
                  <ArrowRight className="h-4 w-4" />
                </Button>
              </Link>
            </motion.div>
          </div>
        </div>
      </section>
    </div>
  );
}
