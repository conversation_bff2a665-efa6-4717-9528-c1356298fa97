"use client";

import { motion, useInView } from "framer-motion";
import { Briefcase } from "lucide-react";
import { useRef } from "react";
import ExperienceList from "./ExperienceList";

export default function Experience() {
  const sectionRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, margin: "-100px" });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  const iconVariants = {
    hidden: { scale: 0, rotate: -45 },
    visible: {
      scale: 1,
      rotate: 0,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 10,
        delay: 0.1,
      },
    },
  };

  return (
    <section id="experience" className="container py-24" ref={sectionRef}>
      <motion.div
        className="mb-16 space-y-4 text-center"
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        variants={containerVariants}
      >
        <motion.div
          className="mb-2 flex justify-center"
          variants={iconVariants}
        >
          <div className="bg-primary/10 text-primary rounded-full p-3">
            <Briefcase className="h-6 w-6" />
          </div>
        </motion.div>
        <motion.span
          className="bg-primary/10 text-primary mb-2 inline-block rounded-full px-4 py-1.5 text-sm font-medium"
          variants={itemVariants}
        >
          Professional Experience
        </motion.span>
        <motion.h2
          className="font-heading text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl"
          variants={itemVariants}
        >
          <span className="from-primary to-primary/60 bg-gradient-to-r bg-clip-text text-transparent">
            My Career Journey
          </span>
        </motion.h2>
        <motion.p
          className="text-muted-foreground mx-auto max-w-[42rem] text-lg"
          variants={itemVariants}
        >
          A timeline of my professional growth and achievements in frontend
          development
        </motion.p>
      </motion.div>

      <motion.div
        className="relative"
        initial={{ opacity: 0 }}
        animate={isInView ? { opacity: 1 } : { opacity: 0 }}
        transition={{ duration: 0.5, delay: 0.3 }}
      >
        {/* Timeline line */}
        <motion.div
          className="bg-primary/30 absolute left-0 h-full w-[2px] origin-top -translate-x-px transform md:left-1/2"
          initial={{ scaleY: 0 }}
          animate={isInView ? { scaleY: 1 } : { scaleY: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
        />
        <ExperienceList isInView={isInView} />
      </motion.div>
    </section>
  );
}
