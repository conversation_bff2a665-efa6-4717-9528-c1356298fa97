"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet";
import { cn } from "@/lib/utils";
import { scrollToSection } from "@/utils/scroll-to-section";
import { AnimatePresence, motion } from "framer-motion";
import {
  Book,
  Code2,
  Contact,
  FileCode2,
  Folders,
  GraduationCap,
  Home,
  Menu,
  Moon,
  Settings,
  Sun,
  Wrench,
  X,
} from "lucide-react";
import { useTheme } from "next-themes";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";

const links = [
  { href: "/", label: "Home", icon: <Home className="h-5 w-5" /> },
  { href: "/about", label: "About", icon: <Book className="h-5 w-5" /> },
  { href: "/skills", label: "Skills", icon: <Code2 className="h-5 w-5" /> },
  {
    href: "/projects",
    label: "Projects",
    icon: <Folders className="h-5 w-5" />,
  },
  {
    href: "/experience",
    label: "Experience",
    icon: <FileCode2 className="h-5 w-5" />,
  },
  {
    href: "/education",
    label: "Education",
    icon: <GraduationCap className="h-5 w-5" />,
  },
  {
    href: "/services",
    label: "Services",
    icon: <Settings className="h-5 w-5" />,
  },
  {
    href: "/tools",
    label: "Tools",
    icon: <Wrench className="h-5 w-5" />,
  },
  { href: "/contact", label: "Contact", icon: <Contact className="h-5 w-5" /> },
];

export default function MobileNav() {
  const { theme, setTheme } = useTheme();
  const pathname = usePathname();
  const [isOpen, setIsOpen] = useState(false);

  const handleClick = (
    e: React.MouseEvent<HTMLAnchorElement>,
    path: string,
  ) => {
    if (path.startsWith("#") && window.location.pathname === "/") {
      e.preventDefault();
      scrollToSection(path.replace("#", ""));
    }
    setIsOpen(false);
  };

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="relative h-10 w-10 rounded-full lg:hidden"
        >
          <Menu className="h-5 w-5" />
          <span className="sr-only">Toggle menu</span>
        </Button>
      </SheetTrigger>
      <SheetContent
        side="right"
        className="flex w-full max-w-[350px] flex-col border-l p-0 backdrop-blur-xl"
      >
        <div className="border-b px-6 py-4">
          <SheetHeader className="flex items-center justify-between">
            <SheetTitle className="font-heading text-left text-xl font-medium">
              <span className="inline-block bg-gradient-to-r from-purple-500 via-blue-500 to-pink-500 bg-clip-text text-transparent">
                Menu
              </span>
            </SheetTitle>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 rounded-full"
              onClick={() => setIsOpen(false)}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close menu</span>
            </Button>
          </SheetHeader>
        </div>

        <nav className="flex-1 overflow-y-auto py-6">
          <motion.div
            className="flex flex-col space-y-2 px-2"
            initial="closed"
            animate="open"
            variants={{
              open: {
                transition: { staggerChildren: 0.07, delayChildren: 0.2 },
              },
              closed: {
                transition: { staggerChildren: 0.05, staggerDirection: -1 },
              },
            }}
          >
            {links.map((link) => {
              const isActive =
                (link.href === "/" && pathname === "/") ||
                (link.href !== "/" && pathname.startsWith(link.href));

              return (
                <motion.div
                  key={link.href}
                  variants={{
                    open: {
                      y: 0,
                      opacity: 1,
                      transition: {
                        y: { stiffness: 1000, velocity: -100 },
                      },
                    },
                    closed: {
                      y: 50,
                      opacity: 0,
                      transition: {
                        y: { stiffness: 1000 },
                      },
                    },
                  }}
                  whileHover={{ scale: 1.03 }}
                  whileTap={{ scale: 0.97 }}
                >
                  <Link
                    href={link.href}
                    onClick={(e) => handleClick(e, link.href)}
                  >
                    <div
                      className={cn(
                        "group relative flex items-center gap-3 rounded-xl px-4 py-3 font-medium transition-all",
                        isActive
                          ? "bg-primary/10 text-primary"
                          : "text-foreground hover:bg-secondary/80 hover:text-primary",
                      )}
                    >
                      <div
                        className={cn(
                          "flex h-10 w-10 items-center justify-center rounded-lg transition-colors",
                          isActive
                            ? "bg-primary/10 text-primary"
                            : "bg-secondary/50 text-muted-foreground group-hover:text-primary",
                        )}
                      >
                        {link.icon}
                      </div>
                      <span>{link.label}</span>

                      {isActive && (
                        <motion.div
                          className="bg-primary absolute right-3 h-2 w-2 rounded-full"
                          layoutId="mobileNavIndicator"
                          transition={{
                            type: "spring",
                            stiffness: 300,
                            damping: 30,
                          }}
                        />
                      )}
                    </div>
                  </Link>
                </motion.div>
              );
            })}
          </motion.div>
        </nav>

        <div className="border-t p-6">
          <SheetFooter>
            <div className="flex w-full items-center justify-between">
              <span className="font-medium">Toggle theme</span>
              <Button
                variant="outline"
                size="icon"
                className="h-10 w-10 rounded-full"
                onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
              >
                <AnimatePresence mode="wait" initial={false}>
                  <motion.div
                    key={theme}
                    initial={{ y: -20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    exit={{ y: 20, opacity: 0 }}
                    transition={{ duration: 0.2 }}
                  >
                    {theme === "dark" ? (
                      <Moon className="h-5 w-5" />
                    ) : (
                      <Sun className="h-5 w-5" />
                    )}
                  </motion.div>
                </AnimatePresence>
                <span className="sr-only">Toggle theme</span>
              </Button>
            </div>
          </SheetFooter>
        </div>
      </SheetContent>
    </Sheet>
  );
}
