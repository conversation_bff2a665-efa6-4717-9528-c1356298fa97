/**
 * Image Optimization Script
 *
 * This script optimizes all images in the public/images directory
 * It converts PNGs to WebP format and compresses JPGs
 *
 * Usage: node scripts/optimize-images.js
 */

const fs = require("fs");
const path = require("path");
const sharp = require("sharp");

const IMAGES_DIR = path.join(process.cwd(), "public", "images");
const WEBP_QUALITY = 80;
const JPEG_QUALITY = 85;

// Create directory if it doesn't exist
if (!fs.existsSync(IMAGES_DIR)) {
  fs.mkdirSync(IMAGES_DIR, { recursive: true });
}

// Get all image files
const getImageFiles = (dir) => {
  const files = fs.readdirSync(dir);
  return files.filter((file) => {
    const ext = path.extname(file).toLowerCase();
    return [".jpg", ".jpeg", ".png", ".gif"].includes(ext);
  });
};

// Process each image
const processImage = async (file) => {
  const filePath = path.join(IMAGES_DIR, file);
  const ext = path.extname(file).toLowerCase();
  const baseName = path.basename(file, ext);
  const webpPath = path.join(IMAGES_DIR, `${baseName}.webp`);

  console.log(`Processing: ${file}`);

  try {
    // Create WebP version
    await sharp(filePath).webp({ quality: WEBP_QUALITY }).toFile(webpPath);

    console.log(`Created WebP: ${baseName}.webp`);

    // If it's a JPEG, optimize it in place
    if (ext === ".jpg" || ext === ".jpeg") {
      const optimizedJpg = await sharp(filePath)
        .jpeg({ quality: JPEG_QUALITY, mozjpeg: true })
        .toBuffer();

      fs.writeFileSync(filePath, optimizedJpg);
      console.log(`Optimized JPEG: ${file}`);
    }
  } catch (error) {
    console.error(`Error processing ${file}:`, error);
  }
};

// Main function
const optimizeImages = async () => {
  const imageFiles = getImageFiles(IMAGES_DIR);

  if (imageFiles.length === 0) {
    console.log("No images found to optimize.");
    return;
  }

  console.log(`Found ${imageFiles.length} images to process...`);

  // Process all images
  for (const file of imageFiles) {
    await processImage(file);
  }

  console.log("Image optimization complete!");
};

// Run the optimization
optimizeImages().catch((err) => {
  console.error("Error during image optimization:", err);
  process.exit(1);
});
