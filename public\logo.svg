<?xml version="1.0" encoding="UTF-8"?>
<svg width="640" height="640" viewBox="0 0 640 640" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Outer glow ring -->
  <circle cx="320" cy="320" r="310" fill="url(#outerGlow)" opacity="0.3"/>

  <!-- Main background with dark yellow gradient -->
  <circle cx="320" cy="320" r="288" fill="url(#bgGradient)"/>

  <!-- Animated rotating rings -->
  <circle cx="320" cy="320" r="260" stroke="url(#glowGradient)" stroke-width="4" stroke-dasharray="15 10" opacity="0.7">
    <animateTransform attributeName="transform" type="rotate" values="0 320 320;360 320 320" dur="8s" repeatCount="indefinite"/>
  </circle>
  <circle cx="320" cy="320" r="240" stroke="url(#pulseGradient)" stroke-width="2" stroke-dasharray="8 12" opacity="0.5">
    <animateTransform attributeName="transform" type="rotate" values="360 320 320;0 320 320" dur="12s" repeatCount="indefinite"/>
  </circle>

  <!-- Inner tech ring -->
  <circle cx="320" cy="320" r="200" stroke="url(#innerRingGradient)" stroke-width="3" stroke-dasharray="20 8" opacity="0.6">
    <animateTransform attributeName="transform" type="rotate" values="0 320 320;-360 320 320" dur="15s" repeatCount="indefinite"/>
  </circle>

  <!-- Central geometric symbol - Abstract tech emblem -->
  <g transform="translate(320,320)" opacity="0.9">
    <!-- Outer diamond frame -->
    <polygon points="0,-80 60,-20 0,80 -60,-20"
             stroke="url(#symbolStroke)"
             stroke-width="6"
             fill="url(#symbolFill)"
             opacity="0.8">
      <animate attributeName="stroke-width" values="4;8;4" dur="3s" repeatCount="indefinite"/>
    </polygon>

    <!-- Inner diamond -->
    <polygon points="0,-50 35,-15 0,50 -35,-15"
             stroke="url(#innerSymbolStroke)"
             stroke-width="4"
             fill="url(#innerSymbolFill)"
             opacity="0.9">
      <animate attributeName="stroke-width" values="3;6;3" dur="2.5s" repeatCount="indefinite"/>
    </polygon>

    <!-- Central core diamond -->
    <polygon points="0,-25 18,-7 0,25 -18,-7"
             fill="url(#coreSymbolFill)"
             opacity="1">
      <animateTransform attributeName="transform" type="rotate" values="0;360" dur="6s" repeatCount="indefinite"/>
    </polygon>

    <!-- Tech connectors -->
    <g opacity="0.8">
      <rect x="-3" y="-100" width="6" height="20" fill="url(#connectorGradient)">
        <animate attributeName="height" values="15;25;15" dur="2s" repeatCount="indefinite"/>
      </rect>
      <rect x="-3" y="80" width="6" height="20" fill="url(#connectorGradient)">
        <animate attributeName="height" values="25;15;25" dur="2s" repeatCount="indefinite"/>
      </rect>
      <rect x="80" y="-3" width="20" height="6" fill="url(#connectorGradient)">
        <animate attributeName="width" values="15;25;15" dur="2s" repeatCount="indefinite"/>
      </rect>
      <rect x="-100" y="-3" width="20" height="6" fill="url(#connectorGradient)">
        <animate attributeName="width" values="25;15;25" dur="2s" repeatCount="indefinite"/>
      </rect>
    </g>
  </g>

  <!-- Orbital elements -->
  <g opacity="0.7">
    <!-- Orbiting nodes -->
    <circle cx="320" cy="180" r="12" fill="url(#orbitNodeGradient)">
      <animateTransform attributeName="transform" type="rotate" values="0 320 320;360 320 320" dur="10s" repeatCount="indefinite"/>
      <animate attributeName="r" values="10;16;10" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="460" cy="320" r="12" fill="url(#orbitNodeGradient)">
      <animateTransform attributeName="transform" type="rotate" values="0 320 320;360 320 320" dur="10s" repeatCount="indefinite"/>
      <animate attributeName="r" values="16;10;16" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="320" cy="460" r="12" fill="url(#orbitNodeGradient)">
      <animateTransform attributeName="transform" type="rotate" values="0 320 320;360 320 320" dur="10s" repeatCount="indefinite"/>
      <animate attributeName="r" values="10;16;10" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="180" cy="320" r="12" fill="url(#orbitNodeGradient)">
      <animateTransform attributeName="transform" type="rotate" values="0 320 320;360 320 320" dur="10s" repeatCount="indefinite"/>
      <animate attributeName="r" values="16;10;16" dur="3s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Energy flow lines -->
  <g opacity="0.6">
    <path d="M320 140L320 180" stroke="url(#flowGradient)" stroke-width="4" stroke-linecap="round">
      <animate attributeName="stroke-dasharray" values="0,40;40,0" dur="2s" repeatCount="indefinite"/>
    </path>
    <path d="M460 320L500 320" stroke="url(#flowGradient)" stroke-width="4" stroke-linecap="round">
      <animate attributeName="stroke-dasharray" values="40,0;0,40" dur="2s" repeatCount="indefinite"/>
    </path>
    <path d="M320 460L320 500" stroke="url(#flowGradient)" stroke-width="4" stroke-linecap="round">
      <animate attributeName="stroke-dasharray" values="0,40;40,0" dur="2s" repeatCount="indefinite"/>
    </path>
    <path d="M140 320L180 320" stroke="url(#flowGradient)" stroke-width="4" stroke-linecap="round">
      <animate attributeName="stroke-dasharray" values="40,0;0,40" dur="2s" repeatCount="indefinite"/>
    </path>
  </g>

  <!-- Corner tech elements -->
  <g opacity="0.8">
    <polygon points="150,150 180,150 180,120 210,120 210,180 150,180"
             fill="url(#cornerTechGradient)" opacity="0.7">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="4s" repeatCount="indefinite"/>
    </polygon>
    <polygon points="490,150 460,150 460,120 430,120 430,180 490,180"
             fill="url(#cornerTechGradient)" opacity="0.7">
      <animate attributeName="opacity" values="1;0.5;1" dur="4s" repeatCount="indefinite"/>
    </polygon>
    <polygon points="150,490 180,490 180,520 210,520 210,460 150,460"
             fill="url(#cornerTechGradient)" opacity="0.7">
      <animate attributeName="opacity" values="0.5;1;0.5" dur="4s" repeatCount="indefinite"/>
    </polygon>
    <polygon points="490,490 460,490 460,520 430,520 430,460 490,460"
             fill="url(#cornerTechGradient)" opacity="0.7">
      <animate attributeName="opacity" values="1;0.5;1" dur="4s" repeatCount="indefinite"/>
    </polygon>
  </g>

  <!-- Floating particles -->
  <g opacity="0.5">
    <circle cx="250" cy="250" r="3" fill="#FCD34D">
      <animateTransform attributeName="transform" type="rotate" values="0 320 320;360 320 320" dur="20s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="4s" repeatCount="indefinite"/>
    </circle>
    <circle cx="390" cy="390" r="2" fill="#F59E0B">
      <animateTransform attributeName="transform" type="rotate" values="360 320 320;0 320 320" dur="25s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="5s" repeatCount="indefinite"/>
    </circle>
    <circle cx="370" cy="270" r="2" fill="#D97706">
      <animateTransform attributeName="transform" type="rotate" values="0 320 320;360 320 320" dur="18s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="3s" repeatCount="indefinite"/>
    </circle>
    <circle cx="270" cy="370" r="3" fill="#FCD34D">
      <animateTransform attributeName="transform" type="rotate" values="360 320 320;0 320 320" dur="22s" repeatCount="indefinite"/>
      <animate attributeName="opacity" values="0;1;0" dur="4.5s" repeatCount="indefinite"/>
    </circle>
  </g>

  <!-- Enhanced gradients with dark yellow theme -->
  <defs>
    <radialGradient id="outerGlow" cx="0.5" cy="0.5" r="0.5">
      <stop offset="0%" stop-color="#F59E0B" stop-opacity="0.1"/>
      <stop offset="100%" stop-color="#D97706" stop-opacity="0.3"/>
    </radialGradient>

    <radialGradient id="bgGradient" cx="0.3" cy="0.3" r="0.8">
      <stop offset="0%" stop-color="#FCD34D"/>
      <stop offset="30%" stop-color="#F59E0B"/>
      <stop offset="70%" stop-color="#D97706"/>
      <stop offset="100%" stop-color="#B45309"/>
    </radialGradient>

    <linearGradient id="glowGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#FCD34D" stop-opacity="0.9"/>
      <stop offset="50%" stop-color="#F59E0B" stop-opacity="0.7"/>
      <stop offset="100%" stop-color="#D97706" stop-opacity="0.9"/>
    </linearGradient>

    <linearGradient id="pulseGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#FCD34D" stop-opacity="0.6"/>
      <stop offset="50%" stop-color="#F59E0B" stop-opacity="0.3"/>
      <stop offset="100%" stop-color="#D97706" stop-opacity="0.6"/>
    </linearGradient>

    <linearGradient id="innerRingGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#F59E0B" stop-opacity="0.8"/>
      <stop offset="50%" stop-color="#FCD34D" stop-opacity="0.6"/>
      <stop offset="100%" stop-color="#D97706" stop-opacity="0.8"/>
    </linearGradient>

    <linearGradient id="symbolStroke" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#B45309"/>
      <stop offset="50%" stop-color="#D97706"/>
      <stop offset="100%" stop-color="#92400E"/>
    </linearGradient>

    <radialGradient id="symbolFill" cx="0.5" cy="0.5" r="0.5">
      <stop offset="0%" stop-color="#FCD34D" stop-opacity="0.3"/>
      <stop offset="100%" stop-color="#F59E0B" stop-opacity="0.1"/>
    </radialGradient>

    <linearGradient id="innerSymbolStroke" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#F59E0B"/>
      <stop offset="50%" stop-color="#FCD34D"/>
      <stop offset="100%" stop-color="#F59E0B"/>
    </linearGradient>

    <radialGradient id="innerSymbolFill" cx="0.5" cy="0.5" r="0.5">
      <stop offset="0%" stop-color="#FCD34D" stop-opacity="0.5"/>
      <stop offset="100%" stop-color="#F59E0B" stop-opacity="0.2"/>
    </radialGradient>

    <radialGradient id="coreSymbolFill" cx="0.5" cy="0.5" r="0.5">
      <stop offset="0%" stop-color="#FEF3C7"/>
      <stop offset="50%" stop-color="#FCD34D"/>
      <stop offset="100%" stop-color="#F59E0B"/>
    </radialGradient>

    <linearGradient id="connectorGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#FCD34D"/>
      <stop offset="50%" stop-color="#F59E0B"/>
      <stop offset="100%" stop-color="#D97706"/>
    </linearGradient>

    <radialGradient id="orbitNodeGradient" cx="0.5" cy="0.5" r="0.5">
      <stop offset="0%" stop-color="#FEF3C7"/>
      <stop offset="50%" stop-color="#FCD34D"/>
      <stop offset="100%" stop-color="#F59E0B"/>
    </radialGradient>

    <linearGradient id="flowGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#FCD34D" stop-opacity="0.8"/>
      <stop offset="50%" stop-color="#F59E0B"/>
      <stop offset="100%" stop-color="#FCD34D" stop-opacity="0.8"/>
    </linearGradient>

    <linearGradient id="cornerTechGradient" x1="0" y1="0" x2="1" y2="1">
      <stop offset="0%" stop-color="#F59E0B" stop-opacity="0.7"/>
      <stop offset="50%" stop-color="#FCD34D" stop-opacity="0.9"/>
      <stop offset="100%" stop-color="#D97706" stop-opacity="0.7"/>
    </linearGradient>
  </defs>
</svg>