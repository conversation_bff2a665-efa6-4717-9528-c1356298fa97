"use client";

import { motion, useReducedMotion } from "framer-motion";
import { memo, useCallback, useEffect, useState } from "react";

// Use a throttle function to limit the frequency of function calls
function throttle(
  func: (e: MouseEvent) => void,
  limit: number,
): (e: MouseEvent) => void {
  let inThrottle: boolean = false;
  return function (e: MouseEvent): void {
    if (!inThrottle) {
      func(e);
      inThrottle = true;
      setTimeout(() => (inThrottle = false), limit);
    }
  };
}

// Memoize the component to prevent unnecessary re-renders
const GlowCursor = memo(function GlowCursor() {
  const [position, setPosition] = useState({ x: -100, y: -100 });
  const [isVisible, setIsVisible] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const prefersReducedMotion = useReducedMotion();

  // Check if device is mobile - don't show cursor on mobile
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);

  // Memoize the checkHovering function to prevent recreating it on each render
  const checkHovering = useCallback(() => {
    const hoveredElement = document.elementFromPoint(position.x, position.y);
    const interactiveElements = ["A", "BUTTON", "INPUT", "SELECT", "TEXTAREA"];

    if (hoveredElement) {
      const isInteractive =
        interactiveElements.includes(hoveredElement.tagName) ||
        hoveredElement.closest('a, button, [role="button"]') !== null;

      setIsHovering(isInteractive);
    } else {
      setIsHovering(false);
    }
  }, [position.x, position.y]);

  useEffect(() => {
    // Skip all cursor effects if on mobile or reduced motion is preferred
    if (isMobile || prefersReducedMotion) return;

    // Throttle the mouse move handler to improve performance
    const updatePosition = throttle((e: MouseEvent) => {
      requestAnimationFrame(() => {
        setPosition({ x: e.clientX, y: e.clientY });
        setIsVisible(true);
      });
    }, 10); // Update at most every 10ms

    const handleMouseLeave = () => setIsVisible(false);
    const handleMouseEnter = () => setIsVisible(true);

    // Add event listeners
    document.addEventListener("mousemove", updatePosition);
    document.addEventListener("mouseleave", handleMouseLeave);
    document.addEventListener("mouseenter", handleMouseEnter);

    // Check hovering state less frequently (200ms instead of 100ms)
    const hoverInterval = setInterval(checkHovering, 200);

    return () => {
      document.removeEventListener("mousemove", updatePosition);
      document.removeEventListener("mouseleave", handleMouseLeave);
      document.removeEventListener("mouseenter", handleMouseEnter);
      clearInterval(hoverInterval);
    };
  }, [position.x, position.y, checkHovering, isMobile, prefersReducedMotion]);

  // Don't render anything if not visible, on mobile, or reduced motion is preferred
  if (!isVisible || isMobile || prefersReducedMotion) return null;

  return (
    <motion.div
      className="pointer-events-none fixed z-[9999] -translate-x-1/2 -translate-y-1/2"
      animate={{
        x: position.x,
        y: position.y,
        scale: isHovering ? 1.5 : 1,
      }}
      transition={{
        type: "spring",
        damping: 30,
        stiffness: 200,
        mass: 0.5,
      }}
      style={{
        opacity: isVisible ? 1 : 0,
        willChange: "transform", // Hint to browser to optimize transforms
      }}
    >
      {/* Simplified cursor with fewer elements for better performance */}
      {/* Combined outer and middle glow into one element */}
      <motion.div
        className="absolute h-6 w-6 -translate-x-1/2 -translate-y-1/2 rounded-full bg-gradient-to-r from-purple-500/20 to-blue-500/20 blur-sm"
        animate={{
          scale: isHovering ? [1, 1.1, 1] : 1,
        }}
        transition={{
          duration: 1,
          repeat: isHovering ? Infinity : 0,
        }}
        style={{ willChange: "transform" }}
      />

      {/* Inner dot */}
      <motion.div
        className="absolute h-2 w-2 -translate-x-1/2 -translate-y-1/2 rounded-full bg-gradient-to-r from-purple-300 to-blue-300"
        animate={{
          scale: isHovering ? 1.5 : 1,
        }}
        transition={{
          duration: 0.2,
        }}
        style={{ willChange: "transform" }}
      />
    </motion.div>
  );
});

export default GlowCursor;
