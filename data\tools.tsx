import {
  <PERSON><PERSON>ircle2,
  <PERSON>2,
  <PERSON><PERSON>ode,
  GitBranch,
  <PERSON>ers,
  Layout,
  LucideIcon,
  Package,
  Palette,
  Rocket,
  TestTube2,
  Workflow,
  Zap,
} from "lucide-react";
import { ReactNode } from "react";

export interface Package {
  name: string;
  description: string;
  npmUrl: string;
  tags: string[];
  features: string[];
  version?: string;
  weeklyDownloads?: string;
  lastUpdated?: string;
}

export interface Tool {
  name: string;
  description: string;
  icon: ReactNode;
  tags: string[];
  features?: string[];
  website: string;
  packages?: Package[];
  version?: string;
  lastUpdated?: string;
  documentation?: string;
  github?: string;
  youtube?: string;
  subscribers?: string;
}

export interface ToolCategory {
  id: string;
  name: string;
  description: string;
  icon: ReactNode;
  items: Tool[];
}

const getIcon = (Icon: LucideIcon) => <Icon className="h-6 w-6" />;

export const toolCategories: ToolCategory[] = [
  {
    id: "frontend-frameworks",
    name: "Frontend Frameworks",
    description: "Modern JavaScript frameworks for building user interfaces",
    icon: getIcon(Code2),
    items: [
      {
        name: "React",
        description:
          "A JavaScript library for building user interfaces with component-based architecture",
        icon: getIcon(Code2),
        tags: ["UI", "Components", "Virtual DOM"],
        features: [
          "Component-based Architecture",
          "Virtual DOM",
          "React Hooks",
          "JSX Syntax",
          "Rich Ecosystem",
        ],
        website: "https://react.dev",
        documentation: "https://react.dev/docs",
        github: "https://github.com/facebook/react",
        packages: [
          {
            name: "React Router",
            description: "Declarative routing for React applications",
            npmUrl: "https://www.npmjs.com/package/react-router-dom",
            tags: ["Routing", "Navigation"],
            features: ["Nested Routes", "Dynamic Routing", "Route Guards"],
            version: "^6.22.0",
            weeklyDownloads: "5M+",
          },
          {
            name: "Redux Toolkit",
            description:
              "The official, opinionated, batteries-included toolset for efficient Redux development",
            npmUrl: "https://www.npmjs.com/package/@reduxjs/toolkit",
            tags: ["State Management", "Redux"],
            features: ["Redux Thunk", "Immutability", "DevTools Integration"],
            version: "^2.1.0",
            weeklyDownloads: "2M+",
          },
          {
            name: "React Query",
            description: "Powerful asynchronous state management for React",
            npmUrl: "https://www.npmjs.com/package/@tanstack/react-query",
            tags: ["Data Fetching", "Caching"],
            features: [
              "Automatic Caching",
              "Background Updates",
              "Error Handling",
            ],
            version: "^5.0.0",
            weeklyDownloads: "1M+",
          },
          {
            name: "@mantine/core",
            description:
              "React components library with 100+ hooks and 40+ components",
            npmUrl: "https://www.npmjs.com/package/@mantine/core",
            tags: ["ui", "components", "hooks"],
            features: ["100+ hooks", "40+ components", "Dark mode"],
            version: "7.5.1",
            weeklyDownloads: "300K+",
            lastUpdated: "2024-02-20",
          },
          {
            name: "shadcn-ui",
            description:
              "Re-usable components built with Radix UI and Tailwind CSS",
            npmUrl: "https://www.npmjs.com/package/@shadcn/ui",
            tags: ["ui", "components", "tailwind"],
            features: ["Copy-paste components", "Customizable", "Accessible"],
            version: "0.0.4",
            weeklyDownloads: "200K+",
            lastUpdated: "2024-02-15",
          },
          {
            name: "react-hook-form",
            description:
              "Performant, flexible and extensible forms with easy-to-use validation",
            npmUrl: "https://www.npmjs.com/package/react-hook-form",
            tags: ["forms", "validation", "performance"],
            features: [
              "Uncontrolled components",
              "Built-in validation",
              "TypeScript support",
            ],
            version: "7.50.1",
            weeklyDownloads: "3M+",
            lastUpdated: "2024-02-18",
          },
          {
            name: "zod",
            description:
              "TypeScript-first schema validation with static type inference",
            npmUrl: "https://www.npmjs.com/package/zod",
            tags: ["validation", "typescript", "schema"],
            features: [
              "Type inference",
              "Runtime validation",
              "Zero dependencies",
            ],
            version: "3.22.4",
            weeklyDownloads: "3M+",
            lastUpdated: "2024-02-20",
          },
          {
            name: "framer-motion",
            description: "Production-ready motion library for React",
            npmUrl: "https://www.npmjs.com/package/framer-motion",
            tags: ["animation", "motion", "gestures"],
            features: ["Spring animations", "Gesture support", "Variants"],
            version: "11.0.3",
            weeklyDownloads: "2M+",
            lastUpdated: "2024-02-15",
          },
          {
            name: "@mongez/react-atom",
            description:
              "A simple and powerful state management tool for React",
            npmUrl: "https://www.npmjs.com/package/@mongez/react-atom",
            tags: ["state-management", "hooks", "typescript"],
            features: ["Atom-based state", "TypeScript support", "SSR ready"],
            version: "5.0.0",
            weeklyDownloads: "10K+",
            lastUpdated: "2024-05-12",
          },
          {
            name: "@mongez/http",
            description:
              "Powerful yet simple package for handling HTTP requests",
            npmUrl: "https://www.npmjs.com/package/@mongez/http",
            tags: ["http", "axios", "api"],
            features: [
              "Request caching",
              "PUT to POST conversion",
              "Request events",
            ],
            version: "2.2.4",
            weeklyDownloads: "5K+",
            lastUpdated: "2024-04-16",
          },
        ],
      },
      {
        name: "Next.js",
        description:
          "React framework for production with server-side rendering and static site generation",
        icon: getIcon(Rocket),
        tags: ["SSR", "SSG", "React"],
        features: [
          "Server-Side Rendering",
          "Static Site Generation",
          "API Routes",
          "File-based Routing",
          "Built-in Optimizations",
        ],
        website: "https://nextjs.org",
        documentation: "https://nextjs.org/docs",
        github: "https://github.com/vercel/next.js",
        packages: [
          {
            name: "NextAuth.js",
            description:
              "Complete authentication solution for Next.js applications",
            npmUrl: "https://www.npmjs.com/package/next-auth",
            tags: ["Authentication", "Security"],
            features: ["OAuth", "JWT", "Session Management"],
            version: "^4.24.0",
            weeklyDownloads: "1M+",
          },
          {
            name: "SWR",
            description: "React Hooks library for data fetching",
            npmUrl: "https://www.npmjs.com/package/swr",
            tags: ["Data Fetching", "Caching"],
            features: [
              "Real-time Updates",
              "Automatic Revalidation",
              "Error Handling",
            ],
            version: "^2.2.0",
            weeklyDownloads: "2M+",
          },
        ],
      },
      {
        name: "Vue.js",
        description:
          "Progressive JavaScript framework for building user interfaces",
        icon: getIcon(Layers),
        tags: ["Progressive", "Reactive", "Composition API"],
        features: [
          "Reactive Data Binding",
          "Component System",
          "Vue Router",
          "Vuex/Pinia",
          "Composition API",
        ],
        website: "https://vuejs.org",
        documentation: "https://vuejs.org/guide/introduction.html",
        github: "https://github.com/vuejs/core",
        packages: [
          {
            name: "Vue Router",
            description: "The official router for Vue.js",
            npmUrl: "https://www.npmjs.com/package/vue-router",
            tags: ["routing", "navigation", "official"],
            features: ["Nested routes", "Route params", "Navigation guards"],
            version: "4.2.5",
            weeklyDownloads: "1.2M+",
            lastUpdated: "2024-02-15",
          },
          {
            name: "Pinia",
            description: "The intuitive store for Vue.js",
            npmUrl: "https://www.npmjs.com/package/pinia",
            tags: ["state-management", "store", "official"],
            features: [
              "TypeScript support",
              "Devtools integration",
              "Modular stores",
            ],
            version: "2.1.7",
            weeklyDownloads: "800K+",
            lastUpdated: "2024-02-10",
          },
          {
            name: "VueUse",
            description: "Collection of Vue Composition Utilities",
            npmUrl: "https://www.npmjs.com/package/@vueuse/core",
            tags: ["composition-api", "utilities", "hooks"],
            features: [
              "200+ composables",
              "TypeScript support",
              "Tree-shakable",
            ],
            version: "10.7.2",
            weeklyDownloads: "500K+",
            lastUpdated: "2024-02-20",
          },
          {
            name: "@kalimahapps/vue-icons",
            description: "Beautiful SVG icons for Vue.js",
            npmUrl: "https://www.npmjs.com/package/@kalimahapps/vue-icons",
            tags: ["icons", "svg", "components"],
            features: ["1000+ icons", "Tree-shakable", "Customizable"],
            version: "1.0.0",
            weeklyDownloads: "50K+",
            lastUpdated: "2024-01-15",
          },
          {
            name: "@vueuse/motion",
            description: "Motion One for Vue",
            npmUrl: "https://www.npmjs.com/package/@vueuse/motion",
            tags: ["animation", "motion", "transitions"],
            features: ["Spring animations", "Gesture support", "Vue 3 ready"],
            version: "2.0.0",
            weeklyDownloads: "30K+",
            lastUpdated: "2024-02-01",
          },
          {
            name: "vee-validate",
            description: "Form validation for Vue.js",
            npmUrl: "https://www.npmjs.com/package/vee-validate",
            tags: ["forms", "validation", "schema"],
            features: [
              "Schema validation",
              "Composition API",
              "TypeScript support",
            ],
            version: "4.12.0",
            weeklyDownloads: "400K+",
            lastUpdated: "2024-02-18",
          },
        ],
      },
      {
        name: "Nuxt.js",
        description:
          "Vue.js framework for building modern web applications with server-side rendering",
        icon: getIcon(Rocket),
        tags: ["Vue", "SSR", "Universal"],
        features: [
          "Server-Side Rendering",
          "Static Site Generation",
          "Auto-imports",
          "File-based Routing",
          "Vue 3 Support",
        ],
        website: "https://nuxt.com",
        documentation: "https://nuxt.com/docs",
        github: "https://github.com/nuxt/nuxt",
        packages: [
          {
            name: "@nuxtjs/auth",
            description: "Authentication module for Nuxt.js",
            npmUrl: "https://www.npmjs.com/package/@nuxtjs/auth",
            tags: ["Authentication", "Security"],
            features: ["Multiple Strategies", "Token Management", "Middleware"],
            version: "^5.0.0",
            weeklyDownloads: "100K+",
          },
          {
            name: "@nuxtjs/pwa",
            description: "Progressive Web App (PWA) module for Nuxt.js",
            npmUrl: "https://www.npmjs.com/package/@nuxtjs/pwa",
            tags: ["PWA", "Offline", "Manifest"],
            features: ["Service Worker", "Manifest", "Meta Tags"],
            version: "^3.3.0",
            weeklyDownloads: "50K+",
          },
        ],
      },
      {
        name: "Angular",
        description:
          "Platform for building mobile and desktop web applications",
        icon: getIcon(Workflow),
        tags: ["TypeScript", "MVC", "Dependency Injection"],
        features: [
          "TypeScript Integration",
          "Dependency Injection",
          "RxJS Integration",
          "Angular CLI",
          "Two-way Binding",
        ],
        website: "https://angular.io",
        documentation: "https://angular.io/docs",
        github: "https://github.com/angular/angular",
        packages: [
          {
            name: "@angular/material",
            description: "Material Design components for Angular",
            npmUrl: "https://www.npmjs.com/package/@angular/material",
            tags: ["UI", "Components", "Material Design"],
            features: ["Accessibility", "Theming", "Responsive"],
            version: "^17.0.0",
            weeklyDownloads: "1M+",
          },
          {
            name: "NgRx",
            description: "Reactive state management for Angular",
            npmUrl: "https://www.npmjs.com/package/@ngrx/store",
            tags: ["State Management", "Redux"],
            features: ["DevTools", "Effects", "Entity Management"],
            version: "^17.0.0",
            weeklyDownloads: "500K+",
          },
        ],
      },
    ],
  },
  {
    id: "ides-editors",
    name: "IDEs & Editors",
    description:
      "Development environments and code editors for efficient coding",
    icon: getIcon(FileCode),
    items: [
      {
        name: "Visual Studio Code",
        description:
          "Primary code editor with powerful extensions for web development",
        icon: getIcon(Code2),
        tags: ["Editor", "Extensions", "Debugging", "IntelliSense"],
        features: ["Live Share", "Git Integration", "Extensions Marketplace"],
        website: "https://code.visualstudio.com",
        documentation: "https://code.visualstudio.com/docs",
        github: "https://github.com/microsoft/vscode",
      },
      {
        name: "WebStorm",
        description:
          "Full-featured JavaScript IDE with advanced refactoring tools",
        icon: getIcon(FileCode),
        tags: ["IDE", "JavaScript", "TypeScript", "Refactoring"],
        features: ["Smart Completion", "Code Analysis", "Built-in Tools"],
        website: "https://www.jetbrains.com/webstorm",
        documentation: "https://www.jetbrains.com/help/webstorm",
      },
    ],
  },
  {
    id: "version-control",
    name: "Version Control & Collaboration",
    description: "Tools for managing code versions and team collaboration",
    icon: getIcon(GitBranch),
    items: [
      {
        name: "Git",
        description:
          "Distributed version control system for tracking code changes",
        icon: getIcon(GitBranch),
        tags: ["VCS", "Collaboration", "History"],
        features: ["Branching", "Merging", "Staging"],
        website: "https://git-scm.com",
        documentation: "https://git-scm.com/doc",
      },
      {
        name: "GitHub",
        description:
          "Platform for hosting and collaborating on Git repositories",
        icon: getIcon(GitBranch),
        tags: ["Hosting", "CI/CD", "Issues"],
        features: ["Actions", "Projects", "Packages"],
        website: "https://github.com",
        documentation: "https://docs.github.com",
      },
    ],
  },
  {
    id: "build-tools",
    name: "Build & Bundling",
    description: "Tools for building and optimizing web applications",
    icon: getIcon(Zap),
    items: [
      {
        name: "Tailwind CSS",
        description:
          "A utility-first CSS framework for rapidly building custom user interfaces",
        icon: getIcon(Zap),
        tags: ["CSS", "Utility-First", "Responsive"],
        features: [
          "Utility-First CSS",
          "Responsive Design",
          "Dark Mode",
          "Customization",
          "JIT Compiler",
        ],
        website: "https://tailwindcss.com",
        documentation: "https://tailwindcss.com/docs",
        github: "https://github.com/tailwindlabs/tailwindcss",
      },
      {
        name: "Webpack",
        description: "Module bundler for JavaScript applications",
        icon: getIcon(Zap),
        tags: ["Bundling", "Optimization", "Loaders"],
        features: ["Code Splitting", "Hot Module Replacement", "Tree Shaking"],
        website: "https://webpack.js.org",
        documentation: "https://webpack.js.org/concepts",
        github: "https://github.com/webpack/webpack",
      },
      {
        name: "Vite",
        description: "Next generation frontend tooling",
        icon: getIcon(Rocket),
        tags: ["Fast", "Modern", "HMR"],
        features: ["ESBuild", "Rollup", "Plugin System"],
        website: "https://vitejs.dev",
        documentation: "https://vitejs.dev/guide",
        github: "https://github.com/vitejs/vite",
      },
    ],
  },
  {
    id: "testing",
    name: "Testing & Quality",
    description: "Tools for testing and ensuring code quality",
    icon: getIcon(TestTube2),
    items: [
      {
        name: "Jest",
        description: "JavaScript testing framework with a focus on simplicity",
        icon: getIcon(TestTube2),
        tags: ["Testing", "Unit", "Integration"],
        features: ["Snapshot Testing", "Mocking", "Coverage"],
        website: "https://jestjs.io",
        documentation: "https://jestjs.io/docs/getting-started",
        github: "https://github.com/jestjs/jest",
      },
      {
        name: "Cypress",
        description: "Modern web testing framework for end-to-end testing",
        icon: getIcon(CheckCircle2),
        tags: ["E2E", "Testing", "Automation"],
        features: ["Real-time Reload", "Time Travel", "Network Control"],
        website: "https://www.cypress.io",
        documentation: "https://docs.cypress.io",
        github: "https://github.com/cypress-io/cypress",
      },
      {
        name: "PageSpeed Insights",
        description:
          "Google's tool for analyzing web page performance and optimization",
        icon: getIcon(Zap),
        tags: ["Performance", "Optimization", "Metrics"],
        features: [
          "Core Web Vitals",
          "Performance Metrics",
          "Mobile Optimization",
          "Best Practices",
          "Performance Scoring",
        ],
        website: "https://pagespeed.web.dev",
        documentation:
          "https://developers.google.com/speed/docs/insights/v5/about",
      },
    ],
  },
  {
    id: "design-tools",
    name: "Design & UI Tools",
    description: "Tools for designing and building user interfaces",
    icon: getIcon(Palette),
    items: [
      {
        name: "Figma",
        description: "Collaborative interface design tool",
        icon: getIcon(Palette),
        tags: ["Design", "UI", "Collaboration"],
        features: ["Components", "Prototyping", "Design Systems"],
        website: "https://www.figma.com",
        documentation: "https://help.figma.com",
      },
      {
        name: "Storybook",
        description: "UI component explorer for frontend developers",
        icon: getIcon(Layout),
        tags: ["Components", "Documentation", "Testing"],
        features: ["Component Library", "Addons", "Visual Testing"],
        website: "https://storybook.js.org",
        documentation:
          "https://storybook.js.org/docs/react/get-started/introduction",
        github: "https://github.com/storybookjs/storybook",
      },
      {
        name: "CSS Filter Converter",
        description: "Convert CSS filters between different formats and values",
        icon: getIcon(Palette),
        tags: ["CSS", "Filters", "Converter"],
        features: [
          "Filter Conversion",
          "Color Effects",
          "Real-time Preview",
          "Multiple Formats",
          "Easy to Use",
        ],
        website: "https://cssfilterconverter.com",
      },
    ],
  },
  {
    id: "youtube-channels",
    name: "YouTube Channels",
    description:
      "Educational and informative YouTube channels for web development",
    icon: getIcon(Layout),
    items: [
      {
        name: "Traversy Media",
        description:
          "Web development tutorials and courses for all skill levels",
        icon: getIcon(Layout),
        tags: ["Tutorials", "Web Development", "Full Stack"],
        features: [
          "HTML/CSS/JavaScript",
          "React/Vue/Angular",
          "Node.js/Express",
          "MongoDB/MySQL",
          "Full Stack Projects",
        ],
        website: "https://www.traversymedia.com",
        youtube: "https://www.youtube.com/@TraversyMedia",
        subscribers: "2.1M+",
      },
      {
        name: "The Net Ninja",
        description: "Modern web development tutorials with clear explanations",
        icon: getIcon(Layout),
        tags: ["Tutorials", "Modern Web", "Frontend"],
        features: [
          "React/Next.js",
          "Vue/Nuxt",
          "TypeScript",
          "Tailwind CSS",
          "Node.js",
        ],
        website: "https://netninja.dev",
        youtube: "https://www.youtube.com/@NetNinja",
        subscribers: "1.1M+",
      },
      {
        name: "Fireship",
        description: "Quick and practical web development tips and tricks",
        icon: getIcon(Layout),
        tags: ["Quick Tips", "Modern Web", "Tutorials"],
        features: [
          "100 Seconds of Code",
          "Web Development Tips",
          "Framework Comparisons",
          "Latest Technologies",
          "Best Practices",
        ],
        website: "https://fireship.io",
        youtube: "https://www.youtube.com/@Fireship",
        subscribers: "2.3M+",
      },
      {
        name: "Kevin Powell",
        description: "CSS and frontend development tutorials and tips",
        icon: getIcon(Layout),
        tags: ["CSS", "Frontend", "Design"],
        features: [
          "CSS Mastery",
          "Responsive Design",
          "CSS Grid/Flexbox",
          "CSS Animations",
          "Best Practices",
        ],
        website: "https://www.kevinpowell.co",
        youtube: "https://www.youtube.com/@KevinPowell",
        subscribers: "1.2M+",
      },
      {
        name: "Academind",
        description: "In-depth web development courses and tutorials",
        icon: getIcon(Layout),
        tags: ["Courses", "Web Development", "Full Stack"],
        features: [
          "React/Next.js",
          "Node.js/Express",
          "MongoDB",
          "TypeScript",
          "Full Stack Projects",
        ],
        website: "https://academind.com",
        youtube: "https://www.youtube.com/@academind",
        subscribers: "800K+",
      },
      {
        name: "Codevolution",
        description: "Modern web development tutorials and courses",
        icon: getIcon(Layout),
        tags: ["React", "Next.js", "TypeScript"],
        features: [
          "React Tutorials",
          "Next.js Series",
          "TypeScript",
          "State Management",
          "Best Practices",
        ],
        website: "https://www.codevolution.dev",
        youtube: "https://www.youtube.com/@Codevolution",
        subscribers: "400K+",
      },
      {
        name: "Elzero Web School",
        description: "Arabic web development tutorials and courses",
        icon: getIcon(Layout),
        tags: ["Arabic", "Web Development", "Full Stack"],
        features: [
          "HTML/CSS/JavaScript",
          "React/Vue/Angular",
          "Node.js/Express",
          "MongoDB/MySQL",
          "Full Stack Projects",
        ],
        website: "https://elzero.org",
        youtube: "https://www.youtube.com/@ElzeroWebSchool",
        subscribers: "1.5M+",
      },
      {
        name: "Codezilla",
        description: "Arabic programming and web development tutorials",
        icon: getIcon(Layout),
        tags: ["Arabic", "Programming", "Web Development"],
        features: [
          "Web Development",
          "Mobile Development",
          "Game Development",
          "UI/UX Design",
          "Programming Basics",
        ],
        website: "https://codezilla.net",
        youtube: "https://www.youtube.com/@Codezilla",
        subscribers: "1.2M+",
      },
      {
        name: "Mohamed Abusrea",
        description: "Arabic web development and programming tutorials",
        icon: getIcon(Layout),
        tags: ["Arabic", "Web Development", "Programming"],
        features: [
          "Frontend Development",
          "Backend Development",
          "Mobile Development",
          "UI/UX Design",
          "Programming Tips",
        ],
        website: "https://mohamedabusrea.com",
        youtube: "https://www.youtube.com/@MohamedAbusrea",
        subscribers: "500K+",
      },
      {
        name: "Abdullah Almehmadi",
        description: "Arabic web development and programming tutorials",
        icon: getIcon(Layout),
        tags: ["Arabic", "Web Development", "Programming"],
        features: [
          "Web Development",
          "Mobile Development",
          "UI/UX Design",
          "Programming Tips",
          "Career Guidance",
        ],
        website: "https://abdullahalmehmadi.com",
        youtube: "https://www.youtube.com/@AbdullahAlmehmadi",
        subscribers: "300K+",
      },
    ],
  },
  {
    id: "job-platforms",
    name: "Job & Freelance Platforms",
    description:
      "Platforms for finding jobs, freelancing opportunities, and career growth",
    icon: getIcon(Workflow),
    items: [
      {
        name: "LinkedIn",
        description: "Professional networking and job search platform",
        icon: getIcon(Workflow),
        tags: ["Networking", "Jobs", "Professional"],
        features: [
          "Professional Networking",
          "Job Search",
          "Company Pages",
          "Skill Endorsements",
          "Career Insights",
        ],
        website: "https://www.linkedin.com",
        documentation: "https://www.linkedin.com/help/linkedin",
      },
      {
        name: "Indeed",
        description: "World's #1 job search site with millions of job listings",
        icon: getIcon(Workflow),
        tags: ["Jobs", "Recruitment", "Career"],
        features: [
          "Job Search",
          "Company Reviews",
          "Salary Insights",
          "Resume Upload",
          "Job Alerts",
        ],
        website: "https://www.indeed.com",
        documentation: "https://www.indeed.com/help",
      },
      {
        name: "Wuzzuf",
        description: "Leading job search platform in Egypt and the Middle East",
        icon: getIcon(Workflow),
        tags: ["Jobs", "Middle East", "Career"],
        features: [
          "Job Search",
          "Company Profiles",
          "Career Resources",
          "Job Alerts",
          "Local Opportunities",
        ],
        website: "https://wuzzuf.net",
        documentation: "https://wuzzuf.net/help",
      },
      {
        name: "Upwork",
        description:
          "Global freelancing platform for finding work and hiring talent",
        icon: getIcon(Workflow),
        tags: ["Freelancing", "Remote Work", "Projects"],
        features: [
          "Project Bidding",
          "Talent Marketplace",
          "Secure Payments",
          "Time Tracking",
          "Client Management",
        ],
        website: "https://www.upwork.com",
        documentation: "https://www.upwork.com/help",
      },
      {
        name: "Fiverr",
        description: "Digital marketplace for freelance services",
        icon: getIcon(Workflow),
        tags: ["Freelancing", "Services", "Digital"],
        features: [
          "Gig Marketplace",
          "Service Packages",
          "Secure Payments",
          "Order Management",
          "Seller Levels",
        ],
        website: "https://www.fiverr.com",
        documentation: "https://www.fiverr.com/support",
      },
      {
        name: "Freelancer",
        description: "Global freelancing and crowdsourcing marketplace",
        icon: getIcon(Workflow),
        tags: ["Freelancing", "Projects", "Contests"],
        features: [
          "Project Bidding",
          "Contests",
          "Milestone Payments",
          "Skill Tests",
          "Portfolio Showcase",
        ],
        website: "https://www.freelancer.com",
        documentation: "https://www.freelancer.com/help",
      },
      {
        name: "Glassdoor",
        description: "Job search and company review platform",
        icon: getIcon(Workflow),
        tags: ["Jobs", "Reviews", "Salaries"],
        features: [
          "Company Reviews",
          "Salary Insights",
          "Interview Reviews",
          "Job Search",
          "Career Resources",
        ],
        website: "https://www.glassdoor.com",
        documentation: "https://help.glassdoor.com",
      },
      {
        name: "Bayt",
        description: "Middle East's leading job site and career portal",
        icon: getIcon(Workflow),
        tags: ["Jobs", "Middle East", "Career"],
        features: [
          "Job Search",
          "Company Profiles",
          "Career Resources",
          "Salary Calculator",
          "Local Opportunities",
        ],
        website: "https://www.bayt.com",
        documentation: "https://www.bayt.com/en/help",
      },
      {
        name: "Mostaql",
        description:
          "Leading Arabic freelancing platform for remote work and projects",
        icon: getIcon(Workflow),
        tags: ["Freelancing", "Arabic", "Remote Work"],
        features: [
          "Project Bidding",
          "Portfolio Showcase",
          "Secure Payments",
          "Client Management",
          "Arabic Community",
        ],
        website: "https://mostaql.com",
        documentation: "https://mostaql.com/help",
      },
      {
        name: "Nafezly",
        description:
          "Arabic freelancing platform connecting clients with skilled professionals",
        icon: getIcon(Workflow),
        tags: ["Freelancing", "Arabic", "Projects"],
        features: [
          "Project Management",
          "Secure Payments",
          "Portfolio Display",
          "Client Communication",
          "Arabic Support",
        ],
        website: "https://nafezly.com",
        documentation: "https://nafezly.com/help",
      },
      {
        name: "El Harefa",
        description:
          "Arabic platform for freelancers and professional services",
        icon: getIcon(Workflow),
        tags: ["Freelancing", "Arabic", "Services"],
        features: [
          "Professional Services",
          "Project Management",
          "Secure Payments",
          "Client Communication",
          "Arabic Community",
        ],
        website: "https://www.elharefa.com/ar",
        documentation: "https://www.elharefa.com/ar/help",
      },
    ],
  },
];
