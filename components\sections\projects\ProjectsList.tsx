"use client";

import { But<PERSON> } from "@/components/ui/button";
import { projects } from "@/data";
import { AnimatePresence, motion } from "framer-motion";
import { Filter } from "lucide-react";
import { useState } from "react";
import ProjectItem from "./ProjectItem";

interface ProjectsListProps {
  limit?: number;
  showFilters?: boolean;
}

export default function ProjectsList({
  limit,
  showFilters = false,
}: ProjectsListProps) {
  const [activeFilter, setActiveFilter] = useState<string>("All");
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  // Extract all unique technologies from projects
  const allTechnologies = [
    "All",
    ...Array.from(new Set(projects.flatMap((project) => project.tech))),
  ].sort();

  // Filter projects based on active filter
  let filteredProjects = projects;
  if (activeFilter !== "All") {
    filteredProjects = projects.filter((project) =>
      project.tech.includes(activeFilter),
    );
  }

  // If limit is provided, only show that many projects
  const displayProjects = limit
    ? filteredProjects.slice(0, limit)
    : filteredProjects;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: "spring",
        stiffness: 260,
        damping: 20,
      },
    },
  };

  return (
    <>
      {showFilters && (
        <div className="mb-12">
          <motion.div
            className="flex justify-center"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Button
              variant="outline"
              size="lg"
              onClick={() => setIsFilterOpen(!isFilterOpen)}
              className="mb-6 gap-2 rounded-full border-2 px-6"
            >
              <Filter className="h-4 w-4" />
              {isFilterOpen ? "Hide Filters" : "Filter Projects"}
            </Button>
          </motion.div>

          <AnimatePresence>
            {isFilterOpen && (
              <motion.div
                className="mb-8 flex flex-wrap justify-center gap-2"
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: "auto" }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
              >
                {allTechnologies.map((tech, index) => (
                  <motion.div
                    key={tech}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05, duration: 0.3 }}
                  >
                    <Button
                      variant={activeFilter === tech ? "default" : "outline"}
                      size="sm"
                      onClick={() => setActiveFilter(tech)}
                      className={`rounded-full transition-all ${
                        activeFilter === tech
                          ? "bg-primary text-primary-foreground"
                          : "hover:border-primary/50 hover:bg-primary/5"
                      }`}
                    >
                      {tech}
                    </Button>
                  </motion.div>
                ))}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}

      <motion.div
        className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {displayProjects.length > 0 ? (
          displayProjects.map((project, index) => (
            <motion.div key={index} variants={itemVariants}>
              <ProjectItem project={project} />
            </motion.div>
          ))
        ) : (
          <motion.div
            className="border-border bg-secondary/10 col-span-full rounded-xl border p-12 text-center"
            variants={itemVariants}
          >
            <p className="text-muted-foreground mb-6 text-lg">
              No projects found with the selected technology.
            </p>
            <Button
              variant="outline"
              onClick={() => setActiveFilter("All")}
              className="rounded-full"
            >
              Show All Projects
            </Button>
          </motion.div>
        )}
      </motion.div>
    </>
  );
}
