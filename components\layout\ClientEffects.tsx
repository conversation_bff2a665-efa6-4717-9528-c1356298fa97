"use client";

import dynamic from "next/dynamic";

// Dynamically import heavy components with ssr=false
// This improves initial page load performance
const MagicBackground = dynamic(
  () => import("@/components/effects/MagicBackground"),
  { ssr: false, loading: () => null },
);

const GlowCursor = dynamic(() => import("@/components/effects/GlowCursor"), {
  ssr: false,
  loading: () => null,
});

const ScrollToTop = dynamic(
  () => import("@/components/sections/scroll-to-top"),
  { ssr: false, loading: () => null },
);

export default function ClientEffects() {
  return (
    <>
      <MagicBackground />
      <GlowCursor />
      <ScrollToTop />
    </>
  );
}
