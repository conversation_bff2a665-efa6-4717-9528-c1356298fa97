"use client";

import { motion, useReducedMotion } from "framer-motion";
import { useTheme } from "next-themes";
import { memo, useEffect, useState } from "react";

// Memoize the component to prevent unnecessary re-renders
const MagicBackground = memo(function MagicBackground() {
  const { theme } = useTheme();
  const [mounted, setMounted] = useState(false);
  const prefersReducedMotion = useReducedMotion();
  const [isVisible, setIsVisible] = useState(true);

  // Check if the component is in viewport to optimize performance
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsVisible(entry.isIntersecting);
      },
      { threshold: 0.1 },
    );

    const element = document.documentElement;
    if (element) {
      observer.observe(element);
    }

    setMounted(true);

    return () => {
      if (element) {
        observer.unobserve(element);
      }
    };
  }, []);

  if (!mounted) return null;

  // Simplified animations for reduced motion preference
  const animationProps = prefersReducedMotion
    ? {}
    : {
        animate: {
          y: [0, 30, 0],
          x: [0, -20, 0],
        },
        transition: {
          duration: 20,
          repeat: Infinity,
          repeatType: "reverse" as const,
        },
      };

  return (
    <div className="pointer-events-none fixed inset-0 -z-50 overflow-hidden">
      {/* Main gradient background */}
      <div
        className={`from-background via-background to-background absolute inset-0 bg-gradient-to-br transition-opacity duration-700 ${
          theme === "dark" ? "opacity-100" : "opacity-0"
        }`}
      />

      {/* Animated gradient orbs - only animate when visible and motion is not reduced */}
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1.5 }}
          className={`absolute inset-0 transition-opacity duration-700 ${
            theme === "dark" ? "opacity-100" : "opacity-30"
          }`}
        >
          {/* Primary gradient - with optimized animation */}
          <motion.div
            className="absolute top-[-50%] left-[10%] h-[80%] w-[80%] rounded-full bg-[radial-gradient(circle_at_center,rgba(125,90,255,0.15),transparent_70%)]"
            {...animationProps}
          />

          {/* Secondary gradient - only render if not reduced motion */}
          {!prefersReducedMotion && (
            <motion.div
              className="absolute right-[-10%] bottom-[-10%] h-[60%] w-[60%] rounded-full bg-[radial-gradient(circle_at_center,rgba(255,90,205,0.15),transparent_70%)]"
              animate={{
                y: [0, -40, 0],
                x: [0, 20, 0],
              }}
              transition={{
                duration: 25,
                repeat: Infinity,
                repeatType: "reverse",
              }}
            />
          )}

          {/* Tertiary gradient - only render if not reduced motion */}
          {!prefersReducedMotion && (
            <motion.div
              className="absolute top-[30%] right-[20%] h-[40%] w-[40%] rounded-full bg-[radial-gradient(circle_at_center,rgba(90,210,255,0.1),transparent_70%)]"
              animate={{
                y: [0, 50, 0],
                x: [0, 30, 0],
              }}
              transition={{
                duration: 18,
                repeat: Infinity,
                repeatType: "reverse",
              }}
            />
          )}
        </motion.div>
      )}

      {/* Grid pattern */}
      <div
        className={`absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.03)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.03)_1px,transparent_1px)] bg-[size:40px_40px] transition-opacity duration-700 ${
          theme === "dark" ? "opacity-20" : "opacity-5"
        }`}
      />

      {/* Subtle noise texture - using SVG instead of PNG for better performance */}
      <div className="absolute inset-0 bg-[url('/images/noise.svg')] opacity-[0.03] mix-blend-overlay" />

      {/* Blur effect - reduced blur amount for better performance */}
      <div
        className={`absolute inset-0 backdrop-blur-[40px] transition-opacity duration-700 ${
          theme === "dark" ? "opacity-20" : "opacity-0"
        }`}
      />
    </div>
  );
});

export default MagicBackground;
