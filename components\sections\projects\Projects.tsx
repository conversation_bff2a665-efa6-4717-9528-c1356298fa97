"use client";

import GradientText from "@/components/effects/GradientText";
import { Button } from "@/components/ui/button";
import { projects } from "@/data";
import { motion } from "framer-motion";
import { ArrowRight, Folder } from "lucide-react";
import Link from "next/link";
import { useRef } from "react";
import ProjectsList from "./ProjectsList";

interface ProjectsProps {
  showAll?: boolean;
}

export default function Projects({ showAll = false }: ProjectsProps) {
  const sectionRef = useRef(null);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5,
      },
    },
  };

  return (
    <section
      id="projects"
      className="relative overflow-hidden py-24 sm:py-32"
      ref={sectionRef}
    >
      {/* Background elements */}
      <div className="absolute inset-0 -z-10">
        <div className="via-background absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5" />
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:40px_40px] [mask-image:radial-gradient(white,transparent_85%)]" />
      </div>

      {/* Decorative elements */}
      <div className="pointer-events-none absolute -top-16 -left-16 h-64 w-64 rounded-full bg-blue-500/5 blur-3xl" />
      <div className="pointer-events-none absolute -right-16 -bottom-16 h-64 w-64 rounded-full bg-purple-500/5 blur-3xl" />

      <div className="relative container">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
          className="mb-16 space-y-4 text-center"
        >
          {showAll && (
            <motion.div className="mb-6" variants={itemVariants}>
              <Link href="/#projects">
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2 rounded-full"
                >
                  <ArrowRight className="h-4 w-4 rotate-180" />
                  Back to Home
                </Button>
              </Link>
            </motion.div>
          )}

          <motion.span
            className="bg-primary/10 text-primary mb-2 inline-block rounded-full px-4 py-1.5 text-sm font-medium"
            variants={itemVariants}
          >
            My Work
          </motion.span>

          <motion.h2
            className="font-heading text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl"
            variants={itemVariants}
          >
            <GradientText>
              {showAll ? "All Projects" : "Featured Projects"}
            </GradientText>
          </motion.h2>

          <motion.p
            className="text-muted-foreground mx-auto max-w-[42rem] text-lg"
            variants={itemVariants}
          >
            {showAll
              ? `A comprehensive showcase of my development work and projects (${projects.length} total)`
              : "A selection of my recent work and personal projects"}
          </motion.p>
        </motion.div>

        <ProjectsList limit={showAll ? undefined : 3} showFilters={showAll} />

        {!showAll && (
          <motion.div
            className="mt-16 text-center"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <Link href="/projects">
              <Button
                size="lg"
                className="hover:shadow-primary/20 gap-2 rounded-full px-8 shadow-lg transition-all"
              >
                <Folder className="h-5 w-5" />
                View All Projects
                <ArrowRight className="h-4 w-4" />
              </Button>
            </Link>
          </motion.div>
        )}
      </div>
    </section>
  );
}
